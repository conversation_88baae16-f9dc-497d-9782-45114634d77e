package workers

import (
	"context"
	"sync"
	"time"

	"carpet-bulk-sms/config"
	"carpet-bulk-sms/pkg/logger"
	"github.com/jmoiron/sqlx"
)

type StatsCollector struct {
	db     *sqlx.DB
	cfg    *config.Config
	logger *logger.Logger
	ctx    context.Context
	cancel context.CancelFunc
	wg     sync.WaitGroup
	running bool
	mu     sync.RWMutex
}

func NewStatsCollector(db *sqlx.DB, cfg *config.Config, log *logger.Logger) *StatsCollector {
	ctx, cancel := context.WithCancel(context.Background())
	
	return &StatsCollector{
		db:     db,
		cfg:    cfg,
		logger: log,
		ctx:    ctx,
		cancel: cancel,
	}
}

// Start запускает сборщик статистики
func (s *StatsCollector) Start() {
	s.mu.Lock()
	if s.running {
		s.mu.Unlock()
		return
	}
	s.running = true
	s.mu.Unlock()

	s.logger.Info("Stats collector started")

	// Собираем статистику каждый час
	ticker := time.NewTicker(time.Hour)
	defer ticker.Stop()

	// Собираем статистику сразу при запуске
	s.collectDailyStats()

	for {
		select {
		case <-s.ctx.Done():
			s.logger.Info("Stats collector stopped")
			return
		case <-ticker.C:
			s.collectDailyStats()
		}
	}
}

// Stop останавливает сборщик статистики
func (s *StatsCollector) Stop() {
	s.mu.Lock()
	if !s.running {
		s.mu.Unlock()
		return
	}
	s.running = false
	s.mu.Unlock()

	s.logger.Info("Stopping stats collector...")
	s.cancel()
	s.wg.Wait()
	s.logger.Info("Stats collector stopped")
}

// IsRunning проверяет, запущен ли сборщик
func (s *StatsCollector) IsRunning() bool {
	s.mu.RLock()
	defer s.mu.RUnlock()
	return s.running
}

// collectDailyStats собирает ежедневную статистику
func (s *StatsCollector) collectDailyStats() {
	today := time.Now().Format("2006-01-02")
	
	s.logger.Debug("Collecting daily stats for %s", today)

	// Собираем статистику по кампаниям
	campaignsCreated, err := s.getCampaignsCreatedToday(today)
	if err != nil {
		s.logger.Error("Failed to get campaigns created today: %v", err)
		return
	}

	// Собираем статистику по SMS
	totalSent, totalDelivered, totalFailed, avgProcessingTime, err := s.getSMSStatsToday(today)
	if err != nil {
		s.logger.Error("Failed to get SMS stats today: %v", err)
		return
	}

	// Сохраняем или обновляем статистику
	err = s.upsertDailyStats(today, campaignsCreated, totalSent, totalDelivered, totalFailed, avgProcessingTime)
	if err != nil {
		s.logger.Error("Failed to upsert daily stats: %v", err)
		return
	}

	s.logger.Info("Daily stats collected: campaigns=%d, sent=%d, delivered=%d, failed=%d", 
		campaignsCreated, totalSent, totalDelivered, totalFailed)
}

// getCampaignsCreatedToday получает количество кампаний, созданных сегодня
func (s *StatsCollector) getCampaignsCreatedToday(date string) (int, error) {
	query := `
		SELECT COUNT(*) 
		FROM sms_bulk_campaigns 
		WHERE DATE(created_at) = $1`

	var count int
	err := s.db.QueryRow(query, date).Scan(&count)
	return count, err
}

// getSMSStatsToday получает статистику SMS за сегодня
func (s *StatsCollector) getSMSStatsToday(date string) (int, int, int, int, error) {
	query := `
		SELECT 
			COUNT(*) FILTER (WHERE status IN (2, 3)) as total_sent,
			COUNT(*) FILTER (WHERE status = 3) as total_delivered,
			COUNT(*) FILTER (WHERE status = 4) as total_failed,
			COALESCE(AVG(EXTRACT(EPOCH FROM (updated_at - created_at)) * 1000)::int, 0) as avg_processing_time_ms
		FROM sms_bulk_queue 
		WHERE DATE(created_at) = $1`

	var totalSent, totalDelivered, totalFailed, avgProcessingTime int
	err := s.db.QueryRow(query, date).Scan(&totalSent, &totalDelivered, &totalFailed, &avgProcessingTime)
	
	return totalSent, totalDelivered, totalFailed, avgProcessingTime, err
}

// upsertDailyStats сохраняет или обновляет ежедневную статистику
func (s *StatsCollector) upsertDailyStats(date string, campaignsCreated, totalSent, totalDelivered, totalFailed, avgProcessingTime int) error {
	query := `
		INSERT INTO sms_bulk_daily_stats 
		(stat_date, campaigns_created, total_sent, total_delivered, total_failed, avg_processing_time_ms, created_at)
		VALUES ($1, $2, $3, $4, $5, $6, NOW())
		ON CONFLICT (stat_date) 
		DO UPDATE SET 
			campaigns_created = EXCLUDED.campaigns_created,
			total_sent = EXCLUDED.total_sent,
			total_delivered = EXCLUDED.total_delivered,
			total_failed = EXCLUDED.total_failed,
			avg_processing_time_ms = EXCLUDED.avg_processing_time_ms,
			created_at = NOW()`

	_, err := s.db.Exec(query, date, campaignsCreated, totalSent, totalDelivered, totalFailed, avgProcessingTime)
	return err
}

// CleanupOldLogs очищает старые логи
func (s *StatsCollector) CleanupOldLogs() error {
	// Получаем настройку хранения логов
	var retentionDays int
	query := `SELECT config_value FROM sms_bulk_config WHERE config_key = 'log_retention_days' AND is_active = true`
	err := s.db.QueryRow(query).Scan(&retentionDays)
	if err != nil {
		retentionDays = 30 // По умолчанию 30 дней
	}

	cutoffDate := time.Now().AddDate(0, 0, -retentionDays)

	// Удаляем старые логи
	deleteQuery := `DELETE FROM sms_bulk_logs WHERE created_at < $1`
	result, err := s.db.Exec(deleteQuery, cutoffDate)
	if err != nil {
		return err
	}

	rowsAffected, _ := result.RowsAffected()
	if rowsAffected > 0 {
		s.logger.Info("Cleaned up %d old log entries", rowsAffected)
	}

	return nil
}

// GetOverallStats возвращает общую статистику
func (s *StatsCollector) GetOverallStats() (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// Общая статистика кампаний
	campaignQuery := `
		SELECT 
			COUNT(*) as total,
			COUNT(*) FILTER (WHERE status = 0) as created,
			COUNT(*) FILTER (WHERE status = 1) as processing,
			COUNT(*) FILTER (WHERE status = 2) as completed,
			COUNT(*) FILTER (WHERE status = 3) as cancelled,
			COUNT(*) FILTER (WHERE status = 4) as failed
		FROM sms_bulk_campaigns`

	var campaignStats struct {
		Total      int `db:"total"`
		Created    int `db:"created"`
		Processing int `db:"processing"`
		Completed  int `db:"completed"`
		Cancelled  int `db:"cancelled"`
		Failed     int `db:"failed"`
	}

	err := s.db.Get(&campaignStats, campaignQuery)
	if err != nil {
		return nil, err
	}

	stats["campaigns"] = campaignStats

	// Общая статистика SMS
	smsQuery := `
		SELECT 
			COUNT(*) as total,
			COUNT(*) FILTER (WHERE status = 0) as pending,
			COUNT(*) FILTER (WHERE status = 1) as processing,
			COUNT(*) FILTER (WHERE status = 2) as sent,
			COUNT(*) FILTER (WHERE status = 3) as delivered,
			COUNT(*) FILTER (WHERE status = 4) as failed,
			COUNT(*) FILTER (WHERE status = 5) as cancelled
		FROM sms_bulk_queue`

	var smsStats struct {
		Total      int `db:"total"`
		Pending    int `db:"pending"`
		Processing int `db:"processing"`
		Sent       int `db:"sent"`
		Delivered  int `db:"delivered"`
		Failed     int `db:"failed"`
		Cancelled  int `db:"cancelled"`
	}

	err = s.db.Get(&smsStats, smsQuery)
	if err != nil {
		return nil, err
	}

	stats["sms"] = smsStats

	// Статистика за последние 7 дней
	weekQuery := `
		SELECT 
			stat_date,
			campaigns_created,
			total_sent,
			total_delivered,
			total_failed
		FROM sms_bulk_daily_stats 
		WHERE stat_date >= CURRENT_DATE - INTERVAL '7 days'
		ORDER BY stat_date DESC`

	var weekStats []map[string]interface{}
	rows, err := s.db.Query(weekQuery)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	for rows.Next() {
		var date string
		var campaigns, sent, delivered, failed int
		
		err := rows.Scan(&date, &campaigns, &sent, &delivered, &failed)
		if err != nil {
			continue
		}

		weekStats = append(weekStats, map[string]interface{}{
			"date":               date,
			"campaigns_created":  campaigns,
			"total_sent":         sent,
			"total_delivered":    delivered,
			"total_failed":       failed,
		})
	}

	stats["last_7_days"] = weekStats
	stats["collected_at"] = time.Now()

	return stats, nil
}
