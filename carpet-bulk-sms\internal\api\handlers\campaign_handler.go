package handlers

import (
	"net/http"
	"strconv"

	"carpet-bulk-sms/internal/models"
	"carpet-bulk-sms/internal/services"
	"carpet-bulk-sms/pkg/logger"
	"github.com/gin-gonic/gin"
)

type CampaignHandler struct {
	campaignService *services.CampaignService
	queueService    *services.QueueService
	logger          *logger.Logger
}

func NewCampaignHandler(campaignService *services.CampaignService, queueService *services.QueueService, log *logger.Logger) *CampaignHandler {
	return &CampaignHandler{
		campaignService: campaignService,
		queueService:    queueService,
		logger:          log,
	}
}

// CreateCampaign создает новую кампанию
func (h *CampaignHandler) CreateCampaign(c *gin.Context) {
	var req models.CreateCampaignRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.J<PERSON>(http.StatusBadRequest, gin.H{
			"error": "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	campaign, err := h.campaignService.CreateCampaign(&req)
	if err != nil {
		h.logger.Error("Failed to create campaign: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to create campaign",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"message": "Campaign created successfully",
		"data": campaign,
	})
}

// CreateCampaignByFilter создает кампанию по фильтрам
func (h *CampaignHandler) CreateCampaignByFilter(c *gin.Context) {
	var req models.CreateCampaignByFilterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	campaign, err := h.campaignService.CreateCampaignByFilter(&req)
	if err != nil {
		h.logger.Error("Failed to create campaign by filter: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to create campaign",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"message": "Campaign created successfully",
		"data": campaign,
	})
}

// GetCampaigns получает список кампаний
func (h *CampaignHandler) GetCampaigns(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	
	var status *int
	if statusStr := c.Query("status"); statusStr != "" {
		if s, err := strconv.Atoi(statusStr); err == nil {
			status = &s
		}
	}

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}

	campaigns, err := h.campaignService.GetCampaigns(page, limit, status)
	if err != nil {
		h.logger.Error("Failed to get campaigns: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to get campaigns",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": campaigns,
	})
}

// GetCampaign получает кампанию по ID
func (h *CampaignHandler) GetCampaign(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid campaign ID",
		})
		return
	}

	campaign, err := h.campaignService.GetCampaign(id)
	if err != nil {
		h.logger.Error("Failed to get campaign: %v", err)
		c.JSON(http.StatusNotFound, gin.H{
			"error": "Campaign not found",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": campaign,
	})
}

// GetCampaignStatus получает статус кампании
func (h *CampaignHandler) GetCampaignStatus(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid campaign ID",
		})
		return
	}

	campaign, err := h.campaignService.GetCampaign(id)
	if err != nil {
		h.logger.Error("Failed to get campaign: %v", err)
		c.JSON(http.StatusNotFound, gin.H{
			"error": "Campaign not found",
		})
		return
	}

	queueStatus, err := h.queueService.GetCampaignQueueStatus(id)
	if err != nil {
		h.logger.Error("Failed to get queue status: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to get queue status",
		})
		return
	}

	response := models.CampaignStatusResponse{
		Campaign:    *campaign,
		QueueStatus: *queueStatus,
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": response,
	})
}

// StartCampaign запускает кампанию
func (h *CampaignHandler) StartCampaign(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid campaign ID",
		})
		return
	}

	err = h.campaignService.StartCampaign(id)
	if err != nil {
		h.logger.Error("Failed to start campaign: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Failed to start campaign",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Campaign started successfully",
	})
}

// StopCampaign останавливает кампанию
func (h *CampaignHandler) StopCampaign(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid campaign ID",
		})
		return
	}

	err = h.campaignService.StopCampaign(id)
	if err != nil {
		h.logger.Error("Failed to stop campaign: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Failed to stop campaign",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Campaign stopped successfully",
	})
}

// GetOverviewStats получает общую статистику
func (h *CampaignHandler) GetOverviewStats(c *gin.Context) {
	// Здесь должна быть реализация получения общей статистики
	// Пока возвращаем заглушку
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"message": "Overview stats not implemented yet",
		},
	})
}

// GetDailyStats получает ежедневную статистику
func (h *CampaignHandler) GetDailyStats(c *gin.Context) {
	// Здесь должна быть реализация получения ежедневной статистики
	// Пока возвращаем заглушку
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"message": "Daily stats not implemented yet",
		},
	})
}

// GetCampaignStats получает статистику кампании
func (h *CampaignHandler) GetCampaignStats(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid campaign ID",
		})
		return
	}

	// Здесь должна быть реализация получения статистики кампании
	// Пока возвращаем заглушку
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"campaign_id": id,
			"message": "Campaign stats not implemented yet",
		},
	})
}
