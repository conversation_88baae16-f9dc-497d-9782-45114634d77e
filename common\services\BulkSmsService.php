<?php

namespace app\common\services;

use Yii;

/**
 * Сервис для взаимодействия с Go микросервисом массовых SMS
 */
class BulkSmsService
{
    private $baseUrl;
    private $apiKey;

    public function __construct()
    {
        $this->baseUrl = Yii::$app->params['bulkSmsServiceUrl'] ?? 'http://127.0.0.1:8080';
        $this->apiKey = Yii::$app->params['bulkSmsServiceApiKey'] ?? 'carpet-bulk-sms-secret-key-2024';
    }

    /**
     * Выполнение HTTP запроса
     * @param string $method HTTP метод
     * @param string $endpoint Endpoint API
     * @param array|null $data Данные для отправки
     * @return array
     * @throws \Exception
     */
    private function makeRequest(string $method, string $endpoint, ?array $data = null): array
    {
        $url = rtrim($this->baseUrl, '/') . '/' . ltrim($endpoint, '/');

        $ch = curl_init();

        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_CUSTOMREQUEST => strtoupper($method),
            CURLOPT_HTTPHEADER => [
                'Authorization: Bearer ' . $this->apiKey,
                'Content-Type: application/json',
                'Accept: application/json',
            ],
        ]);

        if ($data !== null && in_array(strtoupper($method), ['POST', 'PUT', 'PATCH'])) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }

        if (strtoupper($method) === 'GET' && $data !== null) {
            $url .= '?' . http_build_query($data);
            curl_setopt($ch, CURLOPT_URL, $url);
        }

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($response === false) {
            throw new \Exception('cURL error: ' . $error);
        }

        $decodedResponse = json_decode($response, true);

        if ($httpCode >= 400) {
            $errorMessage = $decodedResponse['error'] ?? 'HTTP error ' . $httpCode;
            throw new \Exception($errorMessage);
        }

        return $decodedResponse ?? [];
    }

    /**
     * Создание кампании массовых SMS
     * @param string $name Название кампании
     * @param string $message Текст сообщения
     * @param array $recipients Массив получателей [['client_id' => 1, 'phone' => '+998...', 'name' => 'Имя']]
     * @param array $options Дополнительные опции
     * @return array
     * @throws \Exception
     */
    public function createCampaign(string $name, string $message, array $recipients, array $options = []): array
    {
        try {
            $data = [
                'name' => $name,
                'message' => $message,
                'recipients' => $recipients,
                'sender_nickname' => $options['sender_nickname'] ?? Yii::$app->params['smsNickName'] ?? 'CARPET',
                'priority' => $options['priority'] ?? 5,
            ];

            $result = $this->makeRequest('POST', 'api/v1/campaigns', $data);

            // Логируем успешное создание кампании
            Yii::info([
                'action' => 'bulk_sms_campaign_created',
                'campaign_id' => $result['data']['id'] ?? null,
                'name' => $name,
                'recipients_count' => count($recipients),
            ], 'bulk-sms');

            return $result;
        } catch (\Exception $e) {
            Yii::error('Ошибка создания кампании массовых SMS: ' . $e->getMessage(), 'bulk-sms');
            throw new \Exception('Ошибка при создании кампании массовых SMS: ' . $e->getMessage());
        }
    }

    /**
     * Создание кампании по фильтрам клиентов
     * @param string $name Название кампании
     * @param string $message Текст сообщения
     * @param array $filters Фильтры клиентов
     * @return array
     * @throws \Exception
     */
    public function createCampaignByFilter(string $name, string $message, array $filters): array
    {
        try {
            $data = [
                'name' => $name,
                'message' => $message,
                'filters' => $filters,
            ];

            $result = $this->makeRequest('POST', 'api/v1/campaigns/by-filter', $data);

            // Логируем успешное создание кампании
            Yii::info([
                'action' => 'bulk_sms_campaign_by_filter_created',
                'campaign_id' => $result['data']['id'] ?? null,
                'name' => $name,
                'filters' => $filters,
            ], 'bulk-sms');

            return $result;
        } catch (\Exception $e) {
            Yii::error('Ошибка создания кампании по фильтру: ' . $e->getMessage(), 'bulk-sms');
            throw new \Exception('Ошибка при создании кампании по фильтру: ' . $e->getMessage());
        }
    }

    /**
     * Получение статуса кампании
     * @param int $campaignId ID кампании
     * @return array
     * @throws \Exception
     */
    public function getCampaignStatus(int $campaignId): array
    {
        try {
            return $this->makeRequest('GET', "api/v1/campaigns/{$campaignId}/status");
        } catch (\Exception $e) {
            Yii::error('Ошибка получения статуса кампании: ' . $e->getMessage(), 'bulk-sms');
            throw new \Exception('Ошибка при получении статуса кампании: ' . $e->getMessage());
        }
    }

    /**
     * Получение списка кампаний
     * @param int $page Номер страницы
     * @param int $limit Количество на странице
     * @param int|null $status Фильтр по статусу
     * @return array
     * @throws \Exception
     */
    public function getCampaigns(int $page = 1, int $limit = 20, ?int $status = null): array
    {
        try {
            $params = [
                'page' => $page,
                'limit' => $limit,
            ];

            if ($status !== null) {
                $params['status'] = $status;
            }

            return $this->makeRequest('GET', 'api/v1/campaigns', $params);
        } catch (\Exception $e) {
            Yii::error('Ошибка получения списка кампаний: ' . $e->getMessage(), 'bulk-sms');
            throw new \Exception('Ошибка при получении списка кампаний: ' . $e->getMessage());
        }
    }

    /**
     * Запуск кампании
     * @param int $campaignId ID кампании
     * @return array
     * @throws \Exception
     */
    public function startCampaign(int $campaignId): array
    {
        try {
            $result = $this->makeRequest('POST', "api/v1/campaigns/{$campaignId}/start");

            Yii::info([
                'action' => 'bulk_sms_campaign_started',
                'campaign_id' => $campaignId,
            ], 'bulk-sms');

            return $result;
        } catch (\Exception $e) {
            Yii::error('Ошибка запуска кампании: ' . $e->getMessage(), 'bulk-sms');
            throw new \Exception('Ошибка при запуске кампании: ' . $e->getMessage());
        }
    }

    /**
     * Остановка кампании
     * @param int $campaignId ID кампании
     * @return array
     * @throws \Exception
     */
    public function stopCampaign(int $campaignId): array
    {
        try {
            $result = $this->makeRequest('POST', "api/v1/campaigns/{$campaignId}/stop");

            Yii::info([
                'action' => 'bulk_sms_campaign_stopped',
                'campaign_id' => $campaignId,
            ], 'bulk-sms');

            return $result;
        } catch (\Exception $e) {
            Yii::error('Ошибка остановки кампании: ' . $e->getMessage(), 'bulk-sms');
            throw new \Exception('Ошибка при остановке кампании: ' . $e->getMessage());
        }
    }

    /**
     * Получение статистики очереди
     * @return array
     * @throws \Exception
     */
    public function getQueueStatus(): array
    {
        try {
            $response = $this->client->get('api/v1/queue/status')->send();

            if (!$response->isOk) {
                throw new \Exception('Ошибка получения статуса очереди: ' . $response->content);
            }

            return $response->data;
        } catch (Exception $e) {
            Yii::error('Ошибка получения статуса очереди: ' . $e->getMessage(), 'bulk-sms');
            throw new \Exception('Ошибка при получении статуса очереди: ' . $e->getMessage());
        }
    }

    /**
     * Проверка доступности Go сервиса
     * @return bool
     */
    public function isServiceAvailable(): bool
    {
        try {
            $response = $this->client->get('health')->send();
            return $response->isOk;
        } catch (Exception $e) {
            Yii::error('Go сервис недоступен: ' . $e->getMessage(), 'bulk-sms');
            return false;
        }
    }

    /**
     * Валидация номера телефона
     * @param string $phone
     * @return bool
     */
    public function validatePhone(string $phone): bool
    {
        $pattern = Yii::$app->params['phone_number_regex'] ?? '/^\+998(90|91|93|94|95|97|98|99|50|88|77|33|20)[0-9]{7}$/';
        return preg_match($pattern, $phone);
    }

    /**
     * Форматирование номера телефона
     * @param string $phone
     * @return string
     */
    public function formatPhone(string $phone): string
    {
        // Удаляем все символы кроме цифр и +
        $phone = preg_replace('/[^\d+]/', '', $phone);

        // Если номер начинается с 998, добавляем +
        if (preg_match('/^998\d{9}$/', $phone)) {
            $phone = '+' . $phone;
        }

        // Если номер начинается с 8 или 9, добавляем +998
        if (preg_match('/^[89]\d{8}$/', $phone)) {
            $phone = '+998' . substr($phone, 1);
        }

        return $phone;
    }
}
