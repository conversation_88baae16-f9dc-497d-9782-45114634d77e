<?php

namespace app\common\services;

use Yii;
use yii\httpclient\Client;
use yii\httpclient\Exception;

/**
 * Сервис для взаимодействия с Go микросервисом массовых SMS
 */
class BulkSmsService
{
    private $client;
    private $baseUrl;
    private $apiKey;

    public function __construct()
    {
        $this->baseUrl = Yii::$app->params['bulkSmsServiceUrl'] ?? 'http://127.0.0.1:8080';
        $this->apiKey = Yii::$app->params['bulkSmsServiceApiKey'] ?? 'carpet-bulk-sms-secret-key-2024';
        
        $this->client = new Client([
            'baseUrl' => $this->baseUrl,
            'requestConfig' => [
                'headers' => [
                    'Authorization' => 'Bearer ' . $this->apiKey,
                    'Content-Type' => 'application/json',
                ],
                'timeout' => 30,
            ],
        ]);
    }

    /**
     * Создание кампании массовых SMS
     * @param string $name Название кампании
     * @param string $message Текст сообщения
     * @param array $recipients Массив получателей [['client_id' => 1, 'phone' => '+998...', 'name' => 'Имя']]
     * @param array $options Дополнительные опции
     * @return array
     * @throws \Exception
     */
    public function createCampaign(string $name, string $message, array $recipients, array $options = []): array
    {
        try {
            $data = [
                'name' => $name,
                'message' => $message,
                'recipients' => $recipients,
                'sender_nickname' => $options['sender_nickname'] ?? Yii::$app->params['smsNickName'] ?? 'CARPET',
                'priority' => $options['priority'] ?? 5,
            ];

            $response = $this->client->post('api/v1/campaigns', $data)->send();
            
            if (!$response->isOk) {
                throw new \Exception('Ошибка создания кампании: ' . $response->content);
            }

            $result = $response->data;
            
            // Логируем успешное создание кампании
            Yii::info([
                'action' => 'bulk_sms_campaign_created',
                'campaign_id' => $result['data']['id'] ?? null,
                'name' => $name,
                'recipients_count' => count($recipients),
            ], 'bulk-sms');

            return $result;
        } catch (Exception $e) {
            Yii::error('Ошибка создания кампании массовых SMS: ' . $e->getMessage(), 'bulk-sms');
            throw new \Exception('Ошибка при создании кампании массовых SMS: ' . $e->getMessage());
        }
    }

    /**
     * Создание кампании по фильтрам клиентов
     * @param string $name Название кампании
     * @param string $message Текст сообщения
     * @param array $filters Фильтры клиентов
     * @return array
     * @throws \Exception
     */
    public function createCampaignByFilter(string $name, string $message, array $filters): array
    {
        try {
            $data = [
                'name' => $name,
                'message' => $message,
                'filters' => $filters,
            ];

            $response = $this->client->post('api/v1/campaigns/by-filter', $data)->send();
            
            if (!$response->isOk) {
                throw new \Exception('Ошибка создания кампании по фильтру: ' . $response->content);
            }

            $result = $response->data;
            
            // Логируем успешное создание кампании
            Yii::info([
                'action' => 'bulk_sms_campaign_by_filter_created',
                'campaign_id' => $result['data']['id'] ?? null,
                'name' => $name,
                'filters' => $filters,
            ], 'bulk-sms');

            return $result;
        } catch (Exception $e) {
            Yii::error('Ошибка создания кампании по фильтру: ' . $e->getMessage(), 'bulk-sms');
            throw new \Exception('Ошибка при создании кампании по фильтру: ' . $e->getMessage());
        }
    }

    /**
     * Получение статуса кампании
     * @param int $campaignId ID кампании
     * @return array
     * @throws \Exception
     */
    public function getCampaignStatus(int $campaignId): array
    {
        try {
            $response = $this->client->get("api/v1/campaigns/{$campaignId}/status")->send();
            
            if (!$response->isOk) {
                throw new \Exception('Ошибка получения статуса кампании: ' . $response->content);
            }

            return $response->data;
        } catch (Exception $e) {
            Yii::error('Ошибка получения статуса кампании: ' . $e->getMessage(), 'bulk-sms');
            throw new \Exception('Ошибка при получении статуса кампании: ' . $e->getMessage());
        }
    }

    /**
     * Получение списка кампаний
     * @param int $page Номер страницы
     * @param int $limit Количество на странице
     * @param int|null $status Фильтр по статусу
     * @return array
     * @throws \Exception
     */
    public function getCampaigns(int $page = 1, int $limit = 20, ?int $status = null): array
    {
        try {
            $params = [
                'page' => $page,
                'limit' => $limit,
            ];
            
            if ($status !== null) {
                $params['status'] = $status;
            }

            $response = $this->client->get('api/v1/campaigns', $params)->send();
            
            if (!$response->isOk) {
                throw new \Exception('Ошибка получения списка кампаний: ' . $response->content);
            }

            return $response->data;
        } catch (Exception $e) {
            Yii::error('Ошибка получения списка кампаний: ' . $e->getMessage(), 'bulk-sms');
            throw new \Exception('Ошибка при получении списка кампаний: ' . $e->getMessage());
        }
    }

    /**
     * Запуск кампании
     * @param int $campaignId ID кампании
     * @return array
     * @throws \Exception
     */
    public function startCampaign(int $campaignId): array
    {
        try {
            $response = $this->client->post("api/v1/campaigns/{$campaignId}/start")->send();
            
            if (!$response->isOk) {
                throw new \Exception('Ошибка запуска кампании: ' . $response->content);
            }

            Yii::info([
                'action' => 'bulk_sms_campaign_started',
                'campaign_id' => $campaignId,
            ], 'bulk-sms');

            return $response->data;
        } catch (Exception $e) {
            Yii::error('Ошибка запуска кампании: ' . $e->getMessage(), 'bulk-sms');
            throw new \Exception('Ошибка при запуске кампании: ' . $e->getMessage());
        }
    }

    /**
     * Остановка кампании
     * @param int $campaignId ID кампании
     * @return array
     * @throws \Exception
     */
    public function stopCampaign(int $campaignId): array
    {
        try {
            $response = $this->client->post("api/v1/campaigns/{$campaignId}/stop")->send();
            
            if (!$response->isOk) {
                throw new \Exception('Ошибка остановки кампании: ' . $response->content);
            }

            Yii::info([
                'action' => 'bulk_sms_campaign_stopped',
                'campaign_id' => $campaignId,
            ], 'bulk-sms');

            return $response->data;
        } catch (Exception $e) {
            Yii::error('Ошибка остановки кампании: ' . $e->getMessage(), 'bulk-sms');
            throw new \Exception('Ошибка при остановке кампании: ' . $e->getMessage());
        }
    }

    /**
     * Получение статистики очереди
     * @return array
     * @throws \Exception
     */
    public function getQueueStatus(): array
    {
        try {
            $response = $this->client->get('api/v1/queue/status')->send();
            
            if (!$response->isOk) {
                throw new \Exception('Ошибка получения статуса очереди: ' . $response->content);
            }

            return $response->data;
        } catch (Exception $e) {
            Yii::error('Ошибка получения статуса очереди: ' . $e->getMessage(), 'bulk-sms');
            throw new \Exception('Ошибка при получении статуса очереди: ' . $e->getMessage());
        }
    }

    /**
     * Проверка доступности Go сервиса
     * @return bool
     */
    public function isServiceAvailable(): bool
    {
        try {
            $response = $this->client->get('health')->send();
            return $response->isOk;
        } catch (Exception $e) {
            Yii::error('Go сервис недоступен: ' . $e->getMessage(), 'bulk-sms');
            return false;
        }
    }

    /**
     * Валидация номера телефона
     * @param string $phone
     * @return bool
     */
    public function validatePhone(string $phone): bool
    {
        $pattern = Yii::$app->params['phone_number_regex'] ?? '/^\+998(90|91|93|94|95|97|98|99|50|88|77|33|20)[0-9]{7}$/';
        return preg_match($pattern, $phone);
    }

    /**
     * Форматирование номера телефона
     * @param string $phone
     * @return string
     */
    public function formatPhone(string $phone): string
    {
        // Удаляем все символы кроме цифр и +
        $phone = preg_replace('/[^\d+]/', '', $phone);
        
        // Если номер начинается с 998, добавляем +
        if (preg_match('/^998\d{9}$/', $phone)) {
            $phone = '+' . $phone;
        }
        
        // Если номер начинается с 8 или 9, добавляем +998
        if (preg_match('/^[89]\d{8}$/', $phone)) {
            $phone = '+998' . substr($phone, 1);
        }
        
        return $phone;
    }
}
