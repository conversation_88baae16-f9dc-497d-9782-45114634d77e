package handlers

import (
	"net/http"
	"strconv"

	"carpet-bulk-sms/internal/models"
	"carpet-bulk-sms/internal/services"
	"carpet-bulk-sms/pkg/logger"
	"github.com/gin-gonic/gin"
)

type CallbackHandler struct {
	queueService *services.QueueService
	logger       *logger.Logger
}

func NewCallbackHandler(queueService *services.QueueService, log *logger.Logger) *CallbackHandler {
	return &CallbackHandler{
		queueService: queueService,
		logger:       log,
	}
}

// EskizCallback обрабатывает callback от Eskiz API
func (h *CallbackHandler) EskizCallback(c *gin.Context) {
	var req models.CallbackRequest
	
	// Пытаемся получить данные из JSON
	if err := c.ShouldBindJSON(&req); err != nil {
		// Если не получилось, пытаемся получить из form data
		if err := c.ShouldBind(&req); err != nil {
			h.logger.Error("Failed to bind callback request: %v", err)
			c.J<PERSON>(http.StatusBadRequest, gin.H{
				"error": "Invalid callback format",
			})
			return
		}
	}

	// Получаем queue_id из параметров URL
	queueIDStr := c.Query("queue_id")
	if queueIDStr == "" {
		h.logger.Error("Missing queue_id in callback URL")
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Missing queue_id parameter",
		})
		return
	}

	queueID, err := strconv.ParseInt(queueIDStr, 10, 64)
	if err != nil {
		h.logger.Error("Invalid queue_id: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid queue_id",
		})
		return
	}

	// Логируем полученный callback
	h.logger.Info("Received Eskiz callback: queue_id=%d, status=%s, phone=%s, message_id=%s", 
		queueID, req.Status, req.Phone, req.MessageID)

	// Обрабатываем callback
	err = h.queueService.HandleCallback(queueID, req.Status)
	if err != nil {
		h.logger.Error("Failed to handle callback: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to process callback",
		})
		return
	}

	// Возвращаем успешный ответ
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Callback processed successfully",
	})
}
