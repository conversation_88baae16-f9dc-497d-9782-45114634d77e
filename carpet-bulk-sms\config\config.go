package config

import (
	"fmt"
	"log"
	"os"
	"time"

	"github.com/joho/godotenv"
	"github.com/spf13/cast"
)

type Config struct {
	// Сервер
	HttpPort    string
	HttpHost    string
	LogLevel    string
	Environment string
	ServiceName string

	// База данных
	DbHost            string
	DbPort            string
	DbUser            string
	DbPass            string
	DbName            string
	DbMaxOpenConns    int
	DbMaxIdleConns    int
	DbConnMaxLifetime time.Duration

	// Eskiz API
	EskizUrl      string
	EskizLogin    string
	EskizPassword string
	EskizNickname string

	// Безопасность
	ApiSecretKey string
	AllowedIPs   string

	// Производительность
	QueueBatchSize        int
	QueueProcessInterval  time.Duration
	MaxConcurrentSends    int
	RateLimitPerMinute    int
	RetryDelayMinutes     int

	// Callback
	CallbackBaseUrl string

	// Логирование
	LogFilePath    string
	LogMaxSize     int
	LogMaxBackups  int
	LogMaxAge      int

	// Мониторинг
	EnableMetrics        bool
	MetricsPort          string
	HealthCheckInterval  time.Duration
}

func Load() *Config {
	// Загружаем .env файл
	err := godotenv.Load("./config/.env")
	if err != nil {
		log.Printf("Warning: .env file not found, using environment variables: %v", err)
	}

	c := &Config{}

	// Сервер
	c.HttpPort = cast.ToString(getEnvOrDefault("HTTP_PORT", "8080"))
	c.HttpHost = cast.ToString(getEnvOrDefault("HTTP_HOST", "0.0.0.0"))
	c.LogLevel = cast.ToString(getEnvOrDefault("LOG_LEVEL", "info"))
	c.Environment = cast.ToString(getEnvOrDefault("ENVIRONMENT", "development"))
	c.ServiceName = cast.ToString(getEnvOrDefault("SERVICE_NAME", "carpet-bulk-sms"))

	// База данных
	c.DbHost = cast.ToString(getEnvOrDefault("DB_HOST", "localhost"))
	c.DbPort = cast.ToString(getEnvOrDefault("DB_PORT", "5432"))
	c.DbUser = cast.ToString(getEnvOrDefault("DB_USER", "postgres"))
	c.DbPass = cast.ToString(getEnvOrDefault("DB_PASSWORD", "password"))
	c.DbName = cast.ToString(getEnvOrDefault("DB_NAME", "carpet_db"))
	c.DbMaxOpenConns = cast.ToInt(getEnvOrDefault("DB_MAX_OPEN_CONNS", "25"))
	c.DbMaxIdleConns = cast.ToInt(getEnvOrDefault("DB_MAX_IDLE_CONNS", "5"))
	c.DbConnMaxLifetime = cast.ToDuration(getEnvOrDefault("DB_CONN_MAX_LIFETIME", "300s"))

	// Eskiz API
	c.EskizUrl = cast.ToString(getEnvOrDefault("ESKIZ_URL", "https://notify.eskiz.uz/api"))
	c.EskizLogin = cast.ToString(getEnvOrDefault("ESKIZ_LOGIN", ""))
	c.EskizPassword = cast.ToString(getEnvOrDefault("ESKIZ_PASSWORD", ""))
	c.EskizNickname = cast.ToString(getEnvOrDefault("ESKIZ_NICKNAME", "CARPET"))

	// Безопасность
	c.ApiSecretKey = cast.ToString(getEnvOrDefault("API_SECRET_KEY", "default-secret-key"))
	c.AllowedIPs = cast.ToString(getEnvOrDefault("ALLOWED_IPS", "127.0.0.1"))

	// Производительность
	c.QueueBatchSize = cast.ToInt(getEnvOrDefault("QUEUE_BATCH_SIZE", "100"))
	c.QueueProcessInterval = cast.ToDuration(getEnvOrDefault("QUEUE_PROCESS_INTERVAL", "5s"))
	c.MaxConcurrentSends = cast.ToInt(getEnvOrDefault("MAX_CONCURRENT_SENDS", "50"))
	c.RateLimitPerMinute = cast.ToInt(getEnvOrDefault("RATE_LIMIT_PER_MINUTE", "1000"))
	c.RetryDelayMinutes = cast.ToInt(getEnvOrDefault("RETRY_DELAY_MINUTES", "5"))

	// Callback
	c.CallbackBaseUrl = cast.ToString(getEnvOrDefault("CALLBACK_BASE_URL", "http://localhost:8080"))

	// Логирование
	c.LogFilePath = cast.ToString(getEnvOrDefault("LOG_FILE_PATH", "./logs/bulk-sms.log"))
	c.LogMaxSize = cast.ToInt(getEnvOrDefault("LOG_MAX_SIZE", "100"))
	c.LogMaxBackups = cast.ToInt(getEnvOrDefault("LOG_MAX_BACKUPS", "5"))
	c.LogMaxAge = cast.ToInt(getEnvOrDefault("LOG_MAX_AGE", "30"))

	// Мониторинг
	c.EnableMetrics = cast.ToBool(getEnvOrDefault("ENABLE_METRICS", "true"))
	c.MetricsPort = cast.ToString(getEnvOrDefault("METRICS_PORT", "9090"))
	c.HealthCheckInterval = cast.ToDuration(getEnvOrDefault("HEALTH_CHECK_INTERVAL", "30s"))

	return c
}

func getEnvOrDefault(key string, defaultValue interface{}) interface{} {
	if value, exists := os.LookupEnv(key); exists {
		return value
	}
	return defaultValue
}

// Validate проверяет корректность конфигурации
func (c *Config) Validate() error {
	if c.EskizLogin == "" {
		return fmt.Errorf("ESKIZ_LOGIN is required")
	}
	if c.EskizPassword == "" {
		return fmt.Errorf("ESKIZ_PASSWORD is required")
	}
	if c.DbHost == "" {
		return fmt.Errorf("DB_HOST is required")
	}
	if c.DbName == "" {
		return fmt.Errorf("DB_NAME is required")
	}
	return nil
}
