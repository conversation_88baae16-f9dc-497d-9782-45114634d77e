#!/bin/bash

# Скрипт остановки Go сервиса массовых SMS

set -e

echo "🛑 Stopping Carpet Bulk SMS Service..."

# Переходим в корневую директорию проекта
cd "$(dirname "$0")/.."

PID_FILE="carpet-bulk-sms.pid"

# Проверяем наличие PID файла
if [ ! -f "$PID_FILE" ]; then
    echo "⚠️  PID file not found. Service may not be running."
    
    # Пытаемся найти процесс по имени
    PIDS=$(pgrep -f "carpet-bulk-sms" || true)
    if [ -n "$PIDS" ]; then
        echo "🔍 Found running processes: $PIDS"
        echo "🔪 Killing processes..."
        kill $PIDS
        sleep 2
        
        # Проверяем, что процессы завершились
        REMAINING=$(pgrep -f "carpet-bulk-sms" || true)
        if [ -n "$REMAINING" ]; then
            echo "💀 Force killing remaining processes..."
            kill -9 $REMAINING
        fi
        echo "✅ Service stopped"
    else
        echo "ℹ️  No running processes found"
    fi
    exit 0
fi

# Читаем PID
PID=$(cat "$PID_FILE")

# Проверяем, что процесс существует
if ! ps -p "$PID" > /dev/null 2>&1; then
    echo "⚠️  Process with PID $PID is not running"
    rm -f "$PID_FILE"
    echo "🧹 Removed stale PID file"
    exit 0
fi

# Отправляем SIGTERM для graceful shutdown
echo "📤 Sending SIGTERM to process $PID..."
kill "$PID"

# Ждем завершения процесса
echo "⏳ Waiting for graceful shutdown..."
for i in {1..30}; do
    if ! ps -p "$PID" > /dev/null 2>&1; then
        echo "✅ Service stopped gracefully"
        rm -f "$PID_FILE"
        exit 0
    fi
    sleep 1
done

# Если процесс не завершился, принудительно убиваем
echo "⚠️  Graceful shutdown timeout, force killing..."
kill -9 "$PID" 2>/dev/null || true
sleep 1

# Проверяем, что процесс завершился
if ps -p "$PID" > /dev/null 2>&1; then
    echo "❌ Failed to stop service"
    exit 1
else
    echo "✅ Service force stopped"
    rm -f "$PID_FILE"
fi
