package services

import (
	"database/sql"
	"fmt"
	"time"

	"carpet-bulk-sms/internal/models"
	"carpet-bulk-sms/pkg/logger"
	"github.com/jmoiron/sqlx"
)

type CampaignService struct {
	db     *sqlx.DB
	logger *logger.Logger
}

func NewCampaignService(db *sqlx.DB, log *logger.Logger) *CampaignService {
	return &CampaignService{
		db:     db,
		logger: log,
	}
}

// CreateCampaign создает новую кампанию
func (s *CampaignService) CreateCampaign(req *models.CreateCampaignRequest) (*models.Campaign, error) {
	tx, err := s.db.Beginx()
	if err != nil {
		return nil, fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()

	// Создаем кампанию
	campaign := &models.Campaign{
		Name:            req.Name,
		Message:         req.Message,
		SenderNickname:  req.SenderNickname,
		TotalRecipients: len(req.Recipients),
		Status:          models.CampaignStatusCreated,
		Priority:        req.Priority,
		RetryFailed:     true,
		MaxRetries:      3,
		CreatedAt:       time.Now(),
	}

	if campaign.SenderNickname == "" {
		campaign.SenderNickname = "CARPET"
	}
	if campaign.Priority == 0 {
		campaign.Priority = 5
	}

	query := `
		INSERT INTO sms_bulk_campaigns 
		(name, message, sender_nickname, total_recipients, status, priority, retry_failed, max_retries, created_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
		RETURNING id, created_at`

	err = tx.QueryRowx(query,
		campaign.Name,
		campaign.Message,
		campaign.SenderNickname,
		campaign.TotalRecipients,
		campaign.Status,
		campaign.Priority,
		campaign.RetryFailed,
		campaign.MaxRetries,
		campaign.CreatedAt,
	).Scan(&campaign.ID, &campaign.CreatedAt)

	if err != nil {
		return nil, fmt.Errorf("failed to create campaign: %w", err)
	}

	// Добавляем получателей в очередь
	for _, recipient := range req.Recipients {
		queueItem := &models.QueueItem{
			CampaignID:  campaign.ID,
			PhoneNumber: recipient.Phone,
			Message:     req.Message,
			Status:      models.QueueStatusPending,
			MaxAttempts: 3,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		}

		if recipient.ClientID > 0 {
			queueItem.ClientID = sql.NullInt64{Int64: recipient.ClientID, Valid: true}
		}
		if recipient.Name != "" {
			queueItem.ClientName = sql.NullString{String: recipient.Name, Valid: true}
		}

		queueQuery := `
			INSERT INTO sms_bulk_queue 
			(campaign_id, client_id, phone_number, client_name, message, status, max_attempts, created_at, updated_at)
			VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)`

		_, err = tx.Exec(queueQuery,
			queueItem.CampaignID,
			queueItem.ClientID,
			queueItem.PhoneNumber,
			queueItem.ClientName,
			queueItem.Message,
			queueItem.Status,
			queueItem.MaxAttempts,
			queueItem.CreatedAt,
			queueItem.UpdatedAt,
		)

		if err != nil {
			return nil, fmt.Errorf("failed to add recipient to queue: %w", err)
		}
	}

	if err = tx.Commit(); err != nil {
		return nil, fmt.Errorf("failed to commit transaction: %w", err)
	}

	s.logger.Campaign("create", campaign.ID, map[string]interface{}{
		"name":             campaign.Name,
		"total_recipients": campaign.TotalRecipients,
		"sender_nickname":  campaign.SenderNickname,
	})

	return campaign, nil
}

// CreateCampaignByFilter создает кампанию по фильтрам клиентов
func (s *CampaignService) CreateCampaignByFilter(req *models.CreateCampaignByFilterRequest) (*models.Campaign, error) {
	// Получаем клиентов по фильтрам
	clients, err := s.getClientsByFilter(req.Filters)
	if err != nil {
		return nil, fmt.Errorf("failed to get clients by filter: %w", err)
	}

	if len(clients) == 0 {
		return nil, fmt.Errorf("no clients found matching the filter criteria")
	}

	// Преобразуем клиентов в получателей
	recipients := make([]models.Recipient, 0, len(clients))
	for _, client := range clients {
		if client.HasValidPhone() {
			recipients = append(recipients, models.Recipient{
				ClientID: client.ID,
				Phone:    client.GetPhoneNumber(),
				Name:     client.GetName(),
			})
		}
	}

	if len(recipients) == 0 {
		return nil, fmt.Errorf("no clients with valid phone numbers found")
	}

	// Создаем обычную кампанию
	createReq := &models.CreateCampaignRequest{
		Name:           req.Name,
		Message:        req.Message,
		SenderNickname: req.SenderNickname,
		Priority:       req.Priority,
		Recipients:     recipients,
	}

	return s.CreateCampaign(createReq)
}

// GetCampaign получает кампанию по ID
func (s *CampaignService) GetCampaign(id int64) (*models.Campaign, error) {
	campaign := &models.Campaign{}
	query := `
		SELECT id, name, message, sender_nickname, total_recipients, sent_count, 
		       delivered_count, failed_count, status, created_by_user_id, created_at, 
		       started_at, completed_at, priority, retry_failed, max_retries
		FROM sms_bulk_campaigns 
		WHERE id = $1`

	err := s.db.Get(campaign, query, id)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("campaign not found")
		}
		return nil, fmt.Errorf("failed to get campaign: %w", err)
	}

	return campaign, nil
}

// GetCampaigns получает список кампаний с пагинацией
func (s *CampaignService) GetCampaigns(page, limit int, status *int) (*models.CampaignListResponse, error) {
	offset := (page - 1) * limit

	// Строим запрос
	whereClause := ""
	args := []interface{}{}
	argIndex := 1

	if status != nil {
		whereClause = "WHERE status = $" + fmt.Sprintf("%d", argIndex)
		args = append(args, *status)
		argIndex++
	}

	// Получаем общее количество
	countQuery := "SELECT COUNT(*) FROM sms_bulk_campaigns " + whereClause
	var total int64
	err := s.db.Get(&total, countQuery, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to count campaigns: %w", err)
	}

	// Получаем кампании
	query := fmt.Sprintf(`
		SELECT id, name, message, sender_nickname, total_recipients, sent_count, 
		       delivered_count, failed_count, status, created_by_user_id, created_at, 
		       started_at, completed_at, priority, retry_failed, max_retries
		FROM sms_bulk_campaigns %s
		ORDER BY created_at DESC
		LIMIT $%d OFFSET $%d`, whereClause, argIndex, argIndex+1)

	args = append(args, limit, offset)

	campaigns := []models.Campaign{}
	err = s.db.Select(&campaigns, query, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to get campaigns: %w", err)
	}

	totalPages := int((total + int64(limit) - 1) / int64(limit))

	return &models.CampaignListResponse{
		Campaigns:  campaigns,
		Total:      total,
		Page:       page,
		Limit:      limit,
		TotalPages: totalPages,
	}, nil
}

// StartCampaign запускает кампанию
func (s *CampaignService) StartCampaign(id int64) error {
	campaign, err := s.GetCampaign(id)
	if err != nil {
		return err
	}

	if !campaign.CanBeStarted() {
		return fmt.Errorf("campaign cannot be started, current status: %s", campaign.GetStatusText())
	}

	now := time.Now()
	query := `
		UPDATE sms_bulk_campaigns 
		SET status = $1, started_at = $2 
		WHERE id = $3`

	_, err = s.db.Exec(query, models.CampaignStatusProcessing, now, id)
	if err != nil {
		return fmt.Errorf("failed to start campaign: %w", err)
	}

	s.logger.Campaign("start", id, map[string]interface{}{
		"name": campaign.Name,
	})

	return nil
}

// StopCampaign останавливает кампанию
func (s *CampaignService) StopCampaign(id int64) error {
	campaign, err := s.GetCampaign(id)
	if err != nil {
		return err
	}

	if !campaign.CanBeStopped() {
		return fmt.Errorf("campaign cannot be stopped, current status: %s", campaign.GetStatusText())
	}

	now := time.Now()
	query := `
		UPDATE sms_bulk_campaigns 
		SET status = $1, completed_at = $2 
		WHERE id = $3`

	_, err = s.db.Exec(query, models.CampaignStatusCancelled, now, id)
	if err != nil {
		return fmt.Errorf("failed to stop campaign: %w", err)
	}

	// Отменяем все pending элементы в очереди
	queueQuery := `
		UPDATE sms_bulk_queue 
		SET status = $1, updated_at = $2 
		WHERE campaign_id = $3 AND status = $4`

	_, err = s.db.Exec(queueQuery, models.QueueStatusCancelled, now, id, models.QueueStatusPending)
	if err != nil {
		s.logger.Warn("Failed to cancel pending queue items for campaign %d: %v", id, err)
	}

	s.logger.Campaign("stop", id, map[string]interface{}{
		"name": campaign.Name,
	})

	return nil
}

// UpdateCampaignStats обновляет статистику кампании
func (s *CampaignService) UpdateCampaignStats(campaignID int64) error {
	query := `
		UPDATE sms_bulk_campaigns 
		SET 
			sent_count = (SELECT COUNT(*) FROM sms_bulk_queue WHERE campaign_id = $1 AND status IN ($2, $3)),
			delivered_count = (SELECT COUNT(*) FROM sms_bulk_queue WHERE campaign_id = $1 AND status = $3),
			failed_count = (SELECT COUNT(*) FROM sms_bulk_queue WHERE campaign_id = $1 AND status = $4)
		WHERE id = $1`

	_, err := s.db.Exec(query, campaignID, models.QueueStatusSent, models.QueueStatusDelivered, models.QueueStatusFailed)
	if err != nil {
		return fmt.Errorf("failed to update campaign stats: %w", err)
	}

	return nil
}

// getClientsByFilter получает клиентов по фильтрам
func (s *CampaignService) getClientsByFilter(filters map[string]interface{}) ([]models.Client, error) {
	query := `
		SELECT id, full_name, phone_number, phone_number_2, address, status, region_id, created_at, deleted_at
		FROM clients 
		WHERE deleted_at IS NULL`

	args := []interface{}{}
	argIndex := 1

	// Фильтр по регионам
	if regionIDs, ok := filters["region_ids"].([]interface{}); ok && len(regionIDs) > 0 {
		placeholders := ""
		for i, regionID := range regionIDs {
			if i > 0 {
				placeholders += ","
			}
			placeholders += fmt.Sprintf("$%d", argIndex)
			args = append(args, regionID)
			argIndex++
		}
		query += fmt.Sprintf(" AND region_id IN (%s)", placeholders)
	}

	// Фильтр по статусу
	if status, ok := filters["status"].(float64); ok {
		query += fmt.Sprintf(" AND status = $%d", argIndex)
		args = append(args, int(status))
		argIndex++
	}

	// Фильтр по дате создания
	if createdAfter, ok := filters["created_after"].(string); ok && createdAfter != "" {
		query += fmt.Sprintf(" AND created_at >= $%d", argIndex)
		args = append(args, createdAfter)
		argIndex++
	}

	// Только клиенты с телефонами
	query += " AND phone_number IS NOT NULL AND phone_number != ''"

	// Лимит
	limit := 10000 // Максимальное количество клиентов для массовой рассылки
	if limitVal, ok := filters["limit"].(float64); ok && limitVal > 0 {
		limit = int(limitVal)
	}
	query += fmt.Sprintf(" LIMIT $%d", argIndex)
	args = append(args, limit)

	clients := []models.Client{}
	err := s.db.Select(&clients, query, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to get clients: %w", err)
	}

	return clients, nil
}
