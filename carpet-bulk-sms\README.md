# 📱 Carpet Bulk SMS Service

Высокопроизводительный Go микросервис для массовой отправки SMS через Eskiz.uz API, интегрированный с PHP/Yii2 системой carpet.

## 🚀 Особенности

- **Массовая отправка SMS** через Eskiz.uz API
- **Высокая производительность** - обработка тысяч SMS одновременно
- **Автоматическое управление токенами** Eskiz
- **Обработка callback'ов** для отслеживания статуса доставки
- **Система очередей** с retry механизмом
- **RESTful API** для интеграции с PHP системой
- **Подробное логирование** всех операций
- **Мониторинг и статистика** в реальном времени
- **Graceful shutdown** и управление процессами

## 📋 Требования

- Go 1.21+
- PostgreSQL 12+
- Nginx (для проксирования)
- Аккаунт Eskiz.uz

## 🛠️ Установка

### 1. Клонирование и сборка

```bash
# Скопируйте проект на сервер
cd /var/www/
git clone <repository> go-sms
cd go-sms

# Установите зависимости и соберите
make setup
```

### 2. Конфигурация

Создайте файл `config/.env`:

```env
# Сервер
HTTP_PORT=8080
HTTP_HOST=0.0.0.0
LOG_LEVEL=info
ENVIRONMENT=production

# База данных (та же, что у PHP)
DB_HOST=localhost
DB_PORT=5432
DB_USER=carpet_user
DB_PASSWORD=your_password
DB_NAME=carpet_db

# Eskiz API
ESKIZ_URL=https://notify.eskiz.uz/api
ESKIZ_LOGIN=<EMAIL>
ESKIZ_PASSWORD=Tt03tBBJvyeWwg59E8mmeN51U8LUK1Q40iXNiDtq
ESKIZ_NICKNAME=CARPET

# Безопасность
API_SECRET_KEY=carpet-bulk-sms-secret-key-2024

# Производительность
QUEUE_BATCH_SIZE=100
MAX_CONCURRENT_SENDS=50
RATE_LIMIT_PER_MINUTE=1000

# Callback URL
CALLBACK_BASE_URL=https://carpet.idarmon.uz/bulk-sms
```

### 3. Настройка базы данных

```bash
# Запустите миграции
make migrate
```

### 4. Настройка Nginx

Добавьте в конфигурацию nginx:

```nginx
# Проксирование запросов к Go сервису
location /bulk-sms/ {
    proxy_pass http://127.0.0.1:8080/;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}
```

## 🚀 Запуск

```bash
# Запуск сервиса
make start

# Проверка статуса
make status

# Просмотр логов
make logs

# Остановка
make stop

# Перезапуск
make restart
```

## 📡 API Документация

### Аутентификация

Все API запросы требуют Bearer токен в заголовке:

```
Authorization: Bearer carpet-bulk-sms-secret-key-2024
```

### Основные эндпоинты

#### Создание кампании

```http
POST /api/v1/campaigns
Content-Type: application/json

{
    "name": "Рассылка о скидках",
    "message": "Уважаемый {name}! Скидка 20% на все ковры!",
    "sender_nickname": "CARPET",
    "recipients": [
        {"client_id": 1, "phone": "+998901234567", "name": "Иван"},
        {"client_id": 2, "phone": "+998901234568", "name": "Мария"}
    ]
}
```

#### Создание кампании по фильтрам

```http
POST /api/v1/campaigns/by-filter
Content-Type: application/json

{
    "name": "Рассылка по региону",
    "message": "Специальное предложение для вашего региона!",
    "filters": {
        "region_ids": [1, 2, 3],
        "status": 1
    }
}
```

#### Получение статуса кампании

```http
GET /api/v1/campaigns/{id}/status
```

#### Управление кампанией

```http
POST /api/v1/campaigns/{id}/start   # Запуск
POST /api/v1/campaigns/{id}/stop    # Остановка
```

#### Статус очереди

```http
GET /api/v1/queue/status
```

### Health Check

```http
GET /health
GET /ready
```

## 🔧 Интеграция с PHP

### 1. Создайте сервис в PHP

```php
// common/services/BulkSmsService.php
namespace app\common\services;

use GuzzleHttp\Client;

class BulkSmsService
{
    private $client;
    
    public function __construct()
    {
        $this->client = new Client([
            'base_uri' => 'http://127.0.0.1:8080',
            'headers' => [
                'Authorization' => 'Bearer carpet-bulk-sms-secret-key-2024',
                'Content-Type' => 'application/json',
            ]
        ]);
    }
    
    public function createCampaign($name, $message, $recipients)
    {
        $response = $this->client->post('/api/v1/campaigns', [
            'json' => [
                'name' => $name,
                'message' => $message,
                'recipients' => $recipients
            ]
        ]);
        
        return json_decode($response->getBody(), true);
    }
}
```

### 2. Добавьте в params.php

```php
return [
    // ... существующие параметры
    
    // Go SMS Service
    'bulkSmsServiceUrl' => 'http://127.0.0.1:8080',
    'bulkSmsServiceApiKey' => 'carpet-bulk-sms-secret-key-2024',
];
```

## 📊 Мониторинг

### Проверка статуса

```bash
# Статус сервиса
make status

# HTTP health check
curl http://localhost:8080/health

# Статистика очереди
curl -H "Authorization: Bearer carpet-bulk-sms-secret-key-2024" \
     http://localhost:8080/api/v1/queue/status
```

### Логи

```bash
# Просмотр логов в реальном времени
make logs

# Просмотр конкретного файла
tail -f logs/service.log
```

## 🔒 Безопасность

- API защищен Bearer токенами
- Валидация всех входящих данных
- Логирование всех операций
- Ограничение скорости запросов

## 📈 Производительность

- **Параллельная обработка** до 50 SMS одновременно
- **Батчинг** по 100 элементов за раз
- **Retry механизм** для неудачных отправок
- **Автоматическое управление токенами**

## 🐛 Отладка

### Проблемы с запуском

```bash
# Проверьте конфигурацию
make config

# Проверьте логи
make logs

# Проверьте статус
make status
```

### Проблемы с базой данных

```bash
# Проверьте подключение
psql -h localhost -U carpet_user -d carpet_db -c "SELECT 1;"

# Запустите миграции заново
make migrate
```

### Проблемы с Eskiz

```bash
# Проверьте токен
curl -X POST https://notify.eskiz.uz/api/auth/login \
     -F "email=<EMAIL>" \
     -F "password=Tt03tBBJvyeWwg59E8mmeN51U8LUK1Q40iXNiDtq"
```

## 📞 Поддержка

При возникновении проблем:

1. Проверьте логи: `make logs`
2. Проверьте статус: `make status`
3. Проверьте конфигурацию: `make config`
4. Перезапустите сервис: `make restart`

## 🔄 Обновление

```bash
# Остановите сервис
make stop

# Обновите код
git pull

# Пересоберите
make build

# Запустите миграции (если есть)
make migrate

# Запустите сервис
make start
```
