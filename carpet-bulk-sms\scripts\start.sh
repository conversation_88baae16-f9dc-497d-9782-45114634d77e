#!/bin/bash

# Скрипт запуска Go сервиса массовых SMS

set -e

echo "🚀 Starting Carpet Bulk SMS Service..."

# Переходим в корневую директорию проекта
cd "$(dirname "$0")/.."

# Проверяем наличие исполняемого файла
if [ ! -f "carpet-bulk-sms" ]; then
    echo "❌ Binary not found. Please run ./scripts/build.sh first"
    exit 1
fi

# Проверяем наличие конфигурации
if [ ! -f "config/.env" ]; then
    echo "❌ Configuration file config/.env not found"
    echo "Please create config/.env file with required settings"
    exit 1
fi

# Создаем директории если не существуют
mkdir -p logs
mkdir -p config

# Проверяем, не запущен ли уже сервис
PID_FILE="carpet-bulk-sms.pid"
if [ -f "$PID_FILE" ]; then
    PID=$(cat "$PID_FILE")
    if ps -p "$PID" > /dev/null 2>&1; then
        echo "⚠️  Service is already running with PID $PID"
        echo "Use ./scripts/stop.sh to stop it first"
        exit 1
    else
        echo "🧹 Removing stale PID file"
        rm -f "$PID_FILE"
    fi
fi

# Запускаем сервис в фоновом режиме
echo "🔄 Starting service..."
nohup ./carpet-bulk-sms > logs/service.log 2>&1 &
SERVICE_PID=$!

# Сохраняем PID
echo "$SERVICE_PID" > "$PID_FILE"

# Ждем немного и проверяем, что сервис запустился
sleep 2
if ps -p "$SERVICE_PID" > /dev/null 2>&1; then
    echo "✅ Service started successfully!"
    echo "📋 PID: $SERVICE_PID"
    echo "📄 Log file: logs/service.log"
    echo "🌐 Health check: http://localhost:8080/health"
    echo ""
    echo "Use the following commands:"
    echo "  ./scripts/stop.sh    - Stop the service"
    echo "  ./scripts/restart.sh - Restart the service"
    echo "  ./scripts/status.sh  - Check service status"
    echo "  tail -f logs/service.log - View logs"
else
    echo "❌ Failed to start service"
    rm -f "$PID_FILE"
    exit 1
fi
