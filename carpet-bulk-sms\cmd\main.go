package main

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"carpet-bulk-sms/config"
	"carpet-bulk-sms/internal/api"
	"carpet-bulk-sms/internal/database"
	"carpet-bulk-sms/internal/services"
	"carpet-bulk-sms/internal/workers"
	"carpet-bulk-sms/pkg/logger"
)

func main() {
	// Загружаем конфигурацию
	cfg := config.Load()
	
	// Валидируем конфигурацию
	if err := cfg.Validate(); err != nil {
		fmt.Printf("Configuration error: %v\n", err)
		os.Exit(1)
	}

	// Инициализируем логгер
	log := logger.New(cfg.LogLevel, cfg.LogFilePath)
	log.Info("Starting Carpet Bulk SMS Service")
	log.Info("Environment: %s", cfg.Environment)

	// Подключаемся к базе данных
	db, err := database.Connect(cfg)
	if err != nil {
		log.Fatal("Failed to connect to database: %v", err)
	}
	defer database.Close()

	log.Info("Connected to database successfully")

	// Выполняем миграции
	if err := database.Migrate(db); err != nil {
		log.Fatal("Failed to run migrations: %v", err)
	}
	log.Info("Database migrations completed")

	// Инициализируем сервисы
	eskizService := services.NewEskizService(cfg, log)
	campaignService := services.NewCampaignService(db, log)
	queueService := services.NewQueueService(db, eskizService, log)

	// Проверяем подключение к Eskiz API
	if _, err := eskizService.GetValidToken(); err != nil {
		log.Warn("Failed to get Eskiz token, SMS sending may not work: %v", err)
	} else {
		log.Info("Eskiz API connection verified")
	}

	// Запускаем воркеры
	queueProcessor := workers.NewQueueProcessor(queueService, cfg, log)
	statsCollector := workers.NewStatsCollector(db, cfg, log)

	// Запускаем обработчик очереди в горутине
	go func() {
		log.Info("Starting queue processor")
		queueProcessor.Start()
	}()

	// Запускаем сборщик статистики в горутине
	go func() {
		log.Info("Starting stats collector")
		statsCollector.Start()
	}()

	// Инициализируем HTTP сервер
	router := api.SetupRouter(campaignService, queueService, eskizService, cfg, log)
	
	server := &http.Server{
		Addr:         fmt.Sprintf("%s:%s", cfg.HttpHost, cfg.HttpPort),
		Handler:      router,
		ReadTimeout:  30 * time.Second,
		WriteTimeout: 30 * time.Second,
		IdleTimeout:  60 * time.Second,
	}

	// Запускаем HTTP сервер в горутине
	go func() {
		log.Info("Starting HTTP server on %s:%s", cfg.HttpHost, cfg.HttpPort)
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatal("Failed to start HTTP server: %v", err)
		}
	}()

	// Ждем сигнал для graceful shutdown
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	log.Info("Shutting down server...")

	// Останавливаем воркеры
	queueProcessor.Stop()
	statsCollector.Stop()

	// Graceful shutdown HTTP сервера
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := server.Shutdown(ctx); err != nil {
		log.Error("Server forced to shutdown: %v", err)
	}

	log.Info("Server exited")
}
