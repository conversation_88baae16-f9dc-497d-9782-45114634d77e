package handlers

import (
	"net/http"
	"strconv"

	"carpet-bulk-sms/internal/models"
	"carpet-bulk-sms/internal/services"
	"carpet-bulk-sms/pkg/logger"
	"github.com/gin-gonic/gin"
)

type QueueHandler struct {
	queueService *services.QueueService
	logger       *logger.Logger
}

func NewQueueHandler(queueService *services.QueueService, log *logger.Logger) *QueueHandler {
	return &QueueHandler{
		queueService: queueService,
		logger:       log,
	}
}

// GetStatus получает статус очереди
func (h *QueueHandler) GetStatus(c *gin.Context) {
	stats, err := h.queueService.GetQueueStats()
	if err != nil {
		h.logger.Error("Failed to get queue stats: %v", err)
		c.JSO<PERSON>(http.StatusInternalServerError, gin.H{
			"error": "Failed to get queue status",
			"details": err.Error(),
		})
		return
	}

	c.<PERSON><PERSON><PERSON>(http.StatusOK, gin.H{
		"success": true,
		"data": stats,
	})
}

// RetryFailed повторно отправляет неудачные элементы
func (h *QueueHandler) RetryFailed(c *gin.Context) {
	var req models.RetryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	var campaignID *int64
	if req.CampaignID > 0 {
		campaignID = &req.CampaignID
	}

	retryCount, err := h.queueService.RetryFailedItems(campaignID, req.ItemIDs)
	if err != nil {
		h.logger.Error("Failed to retry failed items: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to retry failed items",
			"details": err.Error(),
		})
		return
	}

	h.logger.Info("Retried %d failed items", retryCount)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Failed items retried successfully",
		"data": gin.H{
			"retried_count": retryCount,
		},
	})
}

// ClearFailed удаляет неудачные элементы
func (h *QueueHandler) ClearFailed(c *gin.Context) {
	campaignIDStr := c.Query("campaign_id")
	var campaignID *int64

	if campaignIDStr != "" {
		if id, err := strconv.ParseInt(campaignIDStr, 10, 64); err == nil {
			campaignID = &id
		}
	}

	clearedCount, err := h.queueService.ClearFailedItems(campaignID)
	if err != nil {
		h.logger.Error("Failed to clear failed items: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to clear failed items",
			"details": err.Error(),
		})
		return
	}

	h.logger.Info("Cleared %d failed items", clearedCount)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Failed items cleared successfully",
		"data": gin.H{
			"cleared_count": clearedCount,
		},
	})
}
