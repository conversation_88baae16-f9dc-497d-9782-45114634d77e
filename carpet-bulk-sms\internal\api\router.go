package api

import (
	"carpet-bulk-sms/config"
	"carpet-bulk-sms/internal/api/handlers"
	"carpet-bulk-sms/internal/api/middleware"
	"carpet-bulk-sms/internal/services"
	"carpet-bulk-sms/pkg/logger"

	"github.com/gin-gonic/gin"
)

// SetupRouter настраивает маршруты API
func SetupRouter(
	campaignService *services.CampaignService,
	queueService *services.QueueService,
	eskizService *services.EskizService,
	cfg *config.Config,
	log *logger.Logger,
) *gin.Engine {
	// Устанавливаем режим Gin
	if cfg.Environment == "production" {
		gin.SetMode(gin.ReleaseMode)
	}

	router := gin.New()

	// Middleware
	router.Use(middleware.Logger(log))
	router.Use(middleware.Recovery(log))
	router.Use(middleware.CORS())

	// Инициализируем обработчики
	campaignHandler := handlers.NewCampaignHandler(campaignService, queueService, log)
	queueHandler := handlers.NewQueueHandler(queueService, log)
	callbackHandler := handlers.NewCallbackHandler(queueService, log)
	healthHandler := handlers.NewHealthHandler(cfg, log)

	// Публичные маршруты (без аутентификации)
	public := router.Group("/")
	{
		public.GET("/health", healthHandler.Health)
		public.GET("/ready", healthHandler.Ready)
		
		// Callback от Eskiz (без аутентификации)
		public.POST("/api/v1/callback/eskiz", callbackHandler.EskizCallback)
	}

	// Защищенные маршруты API
	api := router.Group("/api/v1")
	api.Use(middleware.Auth(cfg))
	{
		// Кампании
		campaigns := api.Group("/campaigns")
		{
			campaigns.POST("", campaignHandler.CreateCampaign)
			campaigns.POST("/by-filter", campaignHandler.CreateCampaignByFilter)
			campaigns.GET("", campaignHandler.GetCampaigns)
			campaigns.GET("/:id", campaignHandler.GetCampaign)
			campaigns.GET("/:id/status", campaignHandler.GetCampaignStatus)
			campaigns.POST("/:id/start", campaignHandler.StartCampaign)
			campaigns.POST("/:id/stop", campaignHandler.StopCampaign)
		}

		// Очередь
		queue := api.Group("/queue")
		{
			queue.GET("/status", queueHandler.GetStatus)
			queue.POST("/retry-failed", queueHandler.RetryFailed)
			queue.POST("/clear-failed", queueHandler.ClearFailed)
		}

		// Статистика
		stats := api.Group("/stats")
		{
			stats.GET("/overview", campaignHandler.GetOverviewStats)
			stats.GET("/daily", campaignHandler.GetDailyStats)
			stats.GET("/campaign/:id", campaignHandler.GetCampaignStats)
		}
	}

	return router
}
