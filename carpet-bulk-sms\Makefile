# Makefile for Carpet Bulk SMS Service

.PHONY: help build start stop restart status clean test deps migrate

# Default target
help:
	@echo "🔨 Carpet Bulk SMS Service - Available Commands:"
	@echo ""
	@echo "📦 Setup:"
	@echo "  make deps     - Install dependencies"
	@echo "  make build    - Build the application"
	@echo "  make migrate  - Run database migrations"
	@echo ""
	@echo "🚀 Service Management:"
	@echo "  make start    - Start the service"
	@echo "  make stop     - Stop the service"
	@echo "  make restart  - Restart the service"
	@echo "  make status   - Check service status"
	@echo ""
	@echo "🧪 Development:"
	@echo "  make test     - Run tests"
	@echo "  make clean    - Clean build artifacts"
	@echo "  make logs     - Show service logs"
	@echo ""
	@echo "📋 Configuration:"
	@echo "  make config   - Show current configuration"

# Install dependencies
deps:
	@echo "📦 Installing dependencies..."
	go mod tidy
	go mod download

# Build the application
build:
	@chmod +x scripts/build.sh
	@./scripts/build.sh

# Start the service
start:
	@chmod +x scripts/start.sh
	@./scripts/start.sh

# Stop the service
stop:
	@chmod +x scripts/stop.sh
	@./scripts/stop.sh

# Restart the service
restart:
	@chmod +x scripts/restart.sh
	@./scripts/restart.sh

# Check service status
status:
	@chmod +x scripts/status.sh
	@./scripts/status.sh

# Run tests
test:
	@echo "🧪 Running tests..."
	go test -v ./...

# Clean build artifacts
clean:
	@echo "🧹 Cleaning build artifacts..."
	rm -f carpet-bulk-sms
	rm -f carpet-bulk-sms.pid
	rm -rf logs/*
	go clean

# Show logs
logs:
	@if [ -f "logs/service.log" ]; then \
		echo "📖 Service logs:"; \
		tail -f logs/service.log; \
	else \
		echo "❌ No log file found"; \
	fi

# Run database migrations
migrate:
	@echo "🗄️  Running database migrations..."
	@if [ -f "carpet-bulk-sms" ]; then \
		./carpet-bulk-sms -migrate; \
	else \
		echo "❌ Binary not found. Run 'make build' first"; \
	fi

# Show current configuration
config:
	@echo "📋 Current configuration:"
	@if [ -f "config/.env" ]; then \
		echo "✅ Configuration file found:"; \
		grep -v "PASSWORD\|SECRET\|TOKEN" config/.env | sed 's/^/  /'; \
	else \
		echo "❌ Configuration file not found"; \
		echo "Please create config/.env file"; \
	fi

# Development mode (with auto-restart)
dev:
	@echo "🔧 Starting in development mode..."
	@if command -v air > /dev/null; then \
		air; \
	else \
		echo "❌ 'air' not found. Install with: go install github.com/cosmtrek/air@latest"; \
	fi

# Install development tools
dev-tools:
	@echo "🛠️  Installing development tools..."
	go install github.com/cosmtrek/air@latest

# Quick setup for new installation
setup: deps build
	@echo "⚙️  Quick setup completed!"
	@echo "Next steps:"
	@echo "1. Configure config/.env file"
	@echo "2. Run 'make migrate' to setup database"
	@echo "3. Run 'make start' to start the service"

# Production deployment
deploy: clean deps build
	@echo "🚀 Production deployment completed!"
	@echo "Service is ready to start with 'make start'"
