package logger

import (
	"fmt"
	"io"
	"os"
	"path/filepath"
	"time"

	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
)

type Logger struct {
	logger zerolog.Logger
}

// New создает новый логгер
func New(level string, logFilePath string) *Logger {
	// Устанавливаем уровень логирования
	logLevel, err := zerolog.ParseLevel(level)
	if err != nil {
		logLevel = zerolog.InfoLevel
	}

	zerolog.SetGlobalLevel(logLevel)

	// Создаем директорию для логов если не существует
	if logFilePath != "" {
		if err := os.MkdirAll(filepath.Dir(logFilePath), 0755); err != nil {
			fmt.Printf("Failed to create log directory: %v\n", err)
		}
	}

	// Настраиваем вывод
	var writers []io.Writer

	// Консольный вывод с красивым форматированием
	consoleWriter := zerolog.ConsoleWriter{
		Out:        os.Stdout,
		TimeFormat: time.RFC3339,
		FormatLevel: func(i interface{}) string {
			return fmt.Sprintf("| %-6s|", i)
		},
		FormatMessage: func(i interface{}) string {
			return fmt.Sprintf("%-50s", i)
		},
		FormatFieldName: func(i interface{}) string {
			return fmt.Sprintf("%s:", i)
		},
		FormatFieldValue: func(i interface{}) string {
			return fmt.Sprintf("%s", i)
		},
	}
	writers = append(writers, consoleWriter)

	// Файловый вывод
	if logFilePath != "" {
		file, err := os.OpenFile(logFilePath, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
		if err != nil {
			fmt.Printf("Failed to open log file: %v\n", err)
		} else {
			writers = append(writers, file)
		}
	}

	// Создаем мультивывод
	multiWriter := io.MultiWriter(writers...)

	// Создаем логгер
	logger := zerolog.New(multiWriter).With().
		Timestamp().
		Str("service", "carpet-bulk-sms").
		Logger()

	return &Logger{
		logger: logger,
	}
}

// Info логирует информационное сообщение
func (l *Logger) Info(format string, args ...interface{}) {
	if len(args) > 0 {
		l.logger.Info().Msgf(format, args...)
	} else {
		l.logger.Info().Msg(format)
	}
}

// Warn логирует предупреждение
func (l *Logger) Warn(format string, args ...interface{}) {
	if len(args) > 0 {
		l.logger.Warn().Msgf(format, args...)
	} else {
		l.logger.Warn().Msg(format)
	}
}

// Error логирует ошибку
func (l *Logger) Error(format string, args ...interface{}) {
	if len(args) > 0 {
		l.logger.Error().Msgf(format, args...)
	} else {
		l.logger.Error().Msg(format)
	}
}

// Debug логирует отладочное сообщение
func (l *Logger) Debug(format string, args ...interface{}) {
	if len(args) > 0 {
		l.logger.Debug().Msgf(format, args...)
	} else {
		l.logger.Debug().Msg(format)
	}
}

// Fatal логирует критическую ошибку и завершает программу
func (l *Logger) Fatal(format string, args ...interface{}) {
	if len(args) > 0 {
		l.logger.Fatal().Msgf(format, args...)
	} else {
		l.logger.Fatal().Msg(format)
	}
}

// WithField добавляет поле к логу
func (l *Logger) WithField(key string, value interface{}) *Logger {
	return &Logger{
		logger: l.logger.With().Interface(key, value).Logger(),
	}
}

// WithFields добавляет несколько полей к логу
func (l *Logger) WithFields(fields map[string]interface{}) *Logger {
	logger := l.logger.With()
	for key, value := range fields {
		logger = logger.Interface(key, value)
	}
	return &Logger{
		logger: logger.Logger(),
	}
}

// HTTP логирует HTTP запрос
func (l *Logger) HTTP(method, path string, statusCode int, duration time.Duration, clientIP string) {
	l.logger.Info().
		Str("method", method).
		Str("path", path).
		Int("status", statusCode).
		Dur("duration", duration).
		Str("client_ip", clientIP).
		Msg("HTTP request")
}

// SMS логирует операцию с SMS
func (l *Logger) SMS(operation, phone string, campaignID int64, success bool, message string) {
	event := l.logger.Info()
	if !success {
		event = l.logger.Error()
	}
	
	event.
		Str("operation", operation).
		Str("phone", phone).
		Int64("campaign_id", campaignID).
		Bool("success", success).
		Str("message", message).
		Msg("SMS operation")
}

// Campaign логирует операцию с кампанией
func (l *Logger) Campaign(operation string, campaignID int64, details map[string]interface{}) {
	event := l.logger.Info().
		Str("operation", operation).
		Int64("campaign_id", campaignID)
	
	for key, value := range details {
		event = event.Interface(key, value)
	}
	
	event.Msg("Campaign operation")
}

// Queue логирует операцию с очередью
func (l *Logger) Queue(operation string, queueSize int, processed int, failed int) {
	l.logger.Info().
		Str("operation", operation).
		Int("queue_size", queueSize).
		Int("processed", processed).
		Int("failed", failed).
		Msg("Queue operation")
}

// Performance логирует метрики производительности
func (l *Logger) Performance(operation string, duration time.Duration, details map[string]interface{}) {
	event := l.logger.Info().
		Str("operation", operation).
		Dur("duration", duration)
	
	for key, value := range details {
		event = event.Interface(key, value)
	}
	
	event.Msg("Performance metric")
}
