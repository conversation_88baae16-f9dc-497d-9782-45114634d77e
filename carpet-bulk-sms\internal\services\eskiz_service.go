package services

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"os"
	"path/filepath"
	"time"

	"carpet-bulk-sms/config"
	"carpet-bulk-sms/internal/models"
	"carpet-bulk-sms/pkg/logger"
)

type EskizService struct {
	cfg    *config.Config
	logger *logger.Logger
	client *http.Client
}

func NewEskizService(cfg *config.Config, log *logger.Logger) *EskizService {
	return &EskizService{
		cfg:    cfg,
		logger: log,
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// EskizTokenResponse структура ответа при получении токена
type EskizTokenResponse struct {
	Message   string `json:"message"`
	Data      struct {
		Token string `json:"token"`
	} `json:"data"`
	TokenType string `json:"token_type"`
}

// EskizSendResponse структура ответа при отправке SMS
type EskizSendResponse struct {
	Status  string `json:"status"`
	Message string `json:"message"`
	ID      string `json:"id"`
}

// GetToken получает новый токен от Eskiz API
func (e *EskizService) GetToken() (string, error) {
	url := fmt.Sprintf("%s/auth/login", e.cfg.EskizUrl)

	payload := &bytes.Buffer{}
	writer := multipart.NewWriter(payload)
	
	_ = writer.WriteField("email", e.cfg.EskizLogin)
	_ = writer.WriteField("password", e.cfg.EskizPassword)
	
	err := writer.Close()
	if err != nil {
		return "", fmt.Errorf("failed to create form data: %w", err)
	}

	req, err := http.NewRequest("POST", url, payload)
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", writer.FormDataContentType())

	resp, err := e.client.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read response: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("eskiz API returned status %d: %s", resp.StatusCode, string(body))
	}

	var tokenResp EskizTokenResponse
	if err := json.Unmarshal(body, &tokenResp); err != nil {
		return "", fmt.Errorf("failed to parse token response: %w", err)
	}

	if tokenResp.Data.Token == "" {
		return "", fmt.Errorf("empty token received from Eskiz")
	}

	// Сохраняем токен в файл
	if err := e.saveToken(tokenResp.Data.Token); err != nil {
		e.logger.Warn("Failed to save token to file: %v", err)
	}

	e.logger.Info("Successfully obtained new Eskiz token")
	return tokenResp.Data.Token, nil
}

// LoadToken загружает токен из файла
func (e *EskizService) LoadToken() (string, error) {
	tokenFile := "./config/eskiz-bulk-token.json"
	
	if _, err := os.Stat(tokenFile); os.IsNotExist(err) {
		return "", fmt.Errorf("token file not found")
	}

	data, err := os.ReadFile(tokenFile)
	if err != nil {
		return "", fmt.Errorf("failed to read token file: %w", err)
	}

	var tokenData struct {
		Token     string    `json:"token"`
		CreatedAt time.Time `json:"created_at"`
	}

	if err := json.Unmarshal(data, &tokenData); err != nil {
		return "", fmt.Errorf("failed to parse token file: %w", err)
	}

	// Проверяем, не истек ли токен (токены Eskiz действуют 24 часа)
	if time.Since(tokenData.CreatedAt) > 23*time.Hour {
		return "", fmt.Errorf("token expired")
	}

	return tokenData.Token, nil
}

// saveToken сохраняет токен в файл
func (e *EskizService) saveToken(token string) error {
	tokenFile := "./config/eskiz-bulk-token.json"
	
	// Создаем директорию если не существует
	if err := os.MkdirAll(filepath.Dir(tokenFile), 0755); err != nil {
		return fmt.Errorf("failed to create config directory: %w", err)
	}

	tokenData := struct {
		Token     string    `json:"token"`
		CreatedAt time.Time `json:"created_at"`
	}{
		Token:     token,
		CreatedAt: time.Now(),
	}

	data, err := json.MarshalIndent(tokenData, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal token data: %w", err)
	}

	if err := os.WriteFile(tokenFile, data, 0644); err != nil {
		return fmt.Errorf("failed to write token file: %w", err)
	}

	return nil
}

// GetValidToken получает валидный токен (из файла или новый)
func (e *EskizService) GetValidToken() (string, error) {
	// Сначала пытаемся загрузить существующий токен
	token, err := e.LoadToken()
	if err == nil {
		return token, nil
	}

	e.logger.Info("Loading new token from Eskiz API: %v", err)

	// Если не удалось загрузить, получаем новый
	return e.GetToken()
}

// SendSMS отправляет SMS через Eskiz API
func (e *EskizService) SendSMS(phone, message, callbackURL string) (*models.EskizResponse, error) {
	token, err := e.GetValidToken()
	if err != nil {
		return nil, fmt.Errorf("failed to get valid token: %w", err)
	}

	url := fmt.Sprintf("%s/message/sms/send", e.cfg.EskizUrl)

	payload := &bytes.Buffer{}
	writer := multipart.NewWriter(payload)
	
	_ = writer.WriteField("mobile_phone", phone)
	_ = writer.WriteField("message", message)
	_ = writer.WriteField("from", e.cfg.EskizNickname)
	
	if callbackURL != "" {
		_ = writer.WriteField("callback_url", callbackURL)
	}
	
	err = writer.Close()
	if err != nil {
		return nil, fmt.Errorf("failed to create form data: %w", err)
	}

	req, err := http.NewRequest("POST", url, payload)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", writer.FormDataContentType())
	req.Header.Set("Authorization", "Bearer "+token)

	resp, err := e.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	// Если токен истек, пытаемся получить новый и повторить запрос
	if resp.StatusCode == http.StatusUnauthorized {
		e.logger.Info("Token expired, getting new token")
		
		newToken, err := e.GetToken()
		if err != nil {
			return nil, fmt.Errorf("failed to refresh token: %w", err)
		}

		// Повторяем запрос с новым токеном
		return e.sendSMSWithToken(phone, message, callbackURL, newToken)
	}

	var eskizResp EskizSendResponse
	if err := json.Unmarshal(body, &eskizResp); err != nil {
		e.logger.Warn("Failed to parse Eskiz response: %v, body: %s", err, string(body))
		// Возвращаем сырой ответ если не удалось распарсить
		return &models.EskizResponse{
			Status:    fmt.Sprintf("http_%d", resp.StatusCode),
			Message:   string(body),
			Timestamp: time.Now(),
		}, nil
	}

	result := &models.EskizResponse{
		Status:    eskizResp.Status,
		Message:   eskizResp.Message,
		ID:        eskizResp.ID,
		Timestamp: time.Now(),
	}

	if resp.StatusCode != http.StatusOK {
		return result, fmt.Errorf("eskiz API error: %s", eskizResp.Message)
	}

	return result, nil
}

// sendSMSWithToken отправляет SMS с указанным токеном
func (e *EskizService) sendSMSWithToken(phone, message, callbackURL, token string) (*models.EskizResponse, error) {
	url := fmt.Sprintf("%s/message/sms/send", e.cfg.EskizUrl)

	payload := &bytes.Buffer{}
	writer := multipart.NewWriter(payload)
	
	_ = writer.WriteField("mobile_phone", phone)
	_ = writer.WriteField("message", message)
	_ = writer.WriteField("from", e.cfg.EskizNickname)
	
	if callbackURL != "" {
		_ = writer.WriteField("callback_url", callbackURL)
	}
	
	err := writer.Close()
	if err != nil {
		return nil, fmt.Errorf("failed to create form data: %w", err)
	}

	req, err := http.NewRequest("POST", url, payload)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", writer.FormDataContentType())
	req.Header.Set("Authorization", "Bearer "+token)

	resp, err := e.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	var eskizResp EskizSendResponse
	if err := json.Unmarshal(body, &eskizResp); err != nil {
		return &models.EskizResponse{
			Status:    fmt.Sprintf("http_%d", resp.StatusCode),
			Message:   string(body),
			Timestamp: time.Now(),
		}, nil
	}

	result := &models.EskizResponse{
		Status:    eskizResp.Status,
		Message:   eskizResp.Message,
		ID:        eskizResp.ID,
		Timestamp: time.Now(),
	}

	if resp.StatusCode != http.StatusOK {
		return result, fmt.Errorf("eskiz API error: %s", eskizResp.Message)
	}

	return result, nil
}
