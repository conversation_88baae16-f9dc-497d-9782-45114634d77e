package database

import (
	"fmt"
	"time"

	"carpet-bulk-sms/config"
	"github.com/jmoiron/sqlx"
	_ "github.com/lib/pq"
)

// DB глобальная переменная для подключения к базе данных
var DB *sqlx.DB

// Connect устанавливает соединение с PostgreSQL
func Connect(cfg *config.Config) (*sqlx.DB, error) {
	dsn := fmt.Sprintf(
		"host=%s port=%s user=%s password=%s dbname=%s sslmode=disable",
		cfg.DbHost,
		cfg.DbPort,
		cfg.DbUser,
		cfg.DbPass,
		cfg.DbName,
	)

	db, err := sqlx.Connect("postgres", dsn)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	// Настройка пула соединений
	db.SetMaxOpenConns(cfg.DbMaxOpenConns)
	db.SetMaxIdleConns(cfg.DbMaxIdleConns)
	db.SetConnMaxLifetime(cfg.DbConnMaxLifetime)

	// Проверка соединения
	if err := db.Ping(); err != nil {
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	DB = db
	return db, nil
}

// Close закрывает соединение с базой данных
func Close() error {
	if DB != nil {
		return DB.Close()
	}
	return nil
}

// Migrate выполняет миграции базы данных
func Migrate(db *sqlx.DB) error {
	migrations := []string{
		createBulkCampaignsTable,
		createBulkQueueTable,
		createBulkLogsTable,
		createBulkConfigTable,
		createBulkDailyStatsTable,
		createIndexes,
		insertInitialConfig,
	}

	for i, migration := range migrations {
		if _, err := db.Exec(migration); err != nil {
			return fmt.Errorf("migration %d failed: %w", i+1, err)
		}
	}

	return nil
}

const createBulkCampaignsTable = `
CREATE TABLE IF NOT EXISTS sms_bulk_campaigns (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    sender_nickname VARCHAR(20) DEFAULT 'CARPET',
    
    -- Статистика
    total_recipients INTEGER DEFAULT 0,
    sent_count INTEGER DEFAULT 0,
    delivered_count INTEGER DEFAULT 0,
    failed_count INTEGER DEFAULT 0,
    
    -- Статусы: 0=created, 1=processing, 2=completed, 3=cancelled, 4=failed
    status SMALLINT DEFAULT 0,
    
    -- Метаданные
    created_by_user_id INTEGER NULL,
    created_at TIMESTAMP DEFAULT NOW(),
    started_at TIMESTAMP NULL,
    completed_at TIMESTAMP NULL,
    
    -- Дополнительные настройки
    priority INTEGER DEFAULT 5,
    retry_failed BOOLEAN DEFAULT TRUE,
    max_retries INTEGER DEFAULT 3
);`

const createBulkQueueTable = `
CREATE TABLE IF NOT EXISTS sms_bulk_queue (
    id BIGSERIAL PRIMARY KEY,
    campaign_id BIGINT NOT NULL REFERENCES sms_bulk_campaigns(id) ON DELETE CASCADE,
    
    -- Получатель
    client_id BIGINT NULL,
    phone_number VARCHAR(20) NOT NULL,
    client_name VARCHAR(255) NULL,
    
    -- Сообщение
    message TEXT NOT NULL,
    
    -- Статусы: 0=pending, 1=processing, 2=sent, 3=delivered, 4=failed, 5=cancelled
    status SMALLINT DEFAULT 0,
    
    -- Eskiz данные
    eskiz_message_id VARCHAR(100) NULL,
    eskiz_response JSONB NULL,
    
    -- Попытки отправки
    attempts INTEGER DEFAULT 0,
    max_attempts INTEGER DEFAULT 3,
    last_attempt_at TIMESTAMP NULL,
    
    -- Результат
    error_message TEXT NULL,
    sent_at TIMESTAMP NULL,
    delivered_at TIMESTAMP NULL,
    
    -- Метаданные
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);`

const createBulkLogsTable = `
CREATE TABLE IF NOT EXISTS sms_bulk_logs (
    id BIGSERIAL PRIMARY KEY,
    
    -- Тип операции
    operation_type VARCHAR(50) NOT NULL,
    
    -- Связанные объекты
    campaign_id BIGINT NULL REFERENCES sms_bulk_campaigns(id),
    queue_item_id BIGINT NULL REFERENCES sms_bulk_queue(id),
    
    -- Данные запроса/ответа
    request_data JSONB NULL,
    response_data JSONB NULL,
    
    -- Результат
    status VARCHAR(20) NOT NULL,
    error_message TEXT NULL,
    
    -- Производительность
    processing_time_ms INTEGER NULL,
    
    -- Метаданные
    ip_address INET NULL,
    user_agent TEXT NULL,
    created_at TIMESTAMP DEFAULT NOW()
);`

const createBulkConfigTable = `
CREATE TABLE IF NOT EXISTS sms_bulk_config (
    id SERIAL PRIMARY KEY,
    config_key VARCHAR(100) UNIQUE NOT NULL,
    config_value TEXT NOT NULL,
    description TEXT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    updated_at TIMESTAMP DEFAULT NOW()
);`

const createBulkDailyStatsTable = `
CREATE TABLE IF NOT EXISTS sms_bulk_daily_stats (
    id SERIAL PRIMARY KEY,
    stat_date DATE NOT NULL,
    campaigns_created INTEGER DEFAULT 0,
    total_sent INTEGER DEFAULT 0,
    total_delivered INTEGER DEFAULT 0,
    total_failed INTEGER DEFAULT 0,
    avg_processing_time_ms INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT NOW(),
    
    UNIQUE(stat_date)
);`

const createIndexes = `
-- Основные индексы для очереди
CREATE INDEX IF NOT EXISTS idx_sms_bulk_queue_status ON sms_bulk_queue(status);
CREATE INDEX IF NOT EXISTS idx_sms_bulk_queue_campaign_status ON sms_bulk_queue(campaign_id, status);
CREATE INDEX IF NOT EXISTS idx_sms_bulk_queue_phone ON sms_bulk_queue(phone_number);
CREATE INDEX IF NOT EXISTS idx_sms_bulk_queue_created_at ON sms_bulk_queue(created_at);
CREATE INDEX IF NOT EXISTS idx_sms_bulk_queue_attempts ON sms_bulk_queue(attempts) WHERE status = 4;

-- Индексы для кампаний
CREATE INDEX IF NOT EXISTS idx_sms_bulk_campaigns_status ON sms_bulk_campaigns(status);
CREATE INDEX IF NOT EXISTS idx_sms_bulk_campaigns_created_at ON sms_bulk_campaigns(created_at);

-- Индексы для логов
CREATE INDEX IF NOT EXISTS idx_sms_bulk_logs_operation ON sms_bulk_logs(operation_type);
CREATE INDEX IF NOT EXISTS idx_sms_bulk_logs_created_at ON sms_bulk_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_sms_bulk_logs_campaign ON sms_bulk_logs(campaign_id);

-- Индекс для статистики
CREATE INDEX IF NOT EXISTS idx_sms_bulk_daily_stats_date ON sms_bulk_daily_stats(stat_date);`

const insertInitialConfig = `
INSERT INTO sms_bulk_config (config_key, config_value, description) VALUES
('queue_batch_size', '100', 'Количество SMS обрабатываемых за раз'),
('queue_process_interval', '5', 'Интервал обработки очереди в секундах'),
('max_concurrent_sends', '50', 'Максимальное количество одновременных отправок'),
('rate_limit_per_minute', '1000', 'Лимит отправок в минуту'),
('retry_delay_minutes', '5', 'Задержка перед повторной попыткой в минутах'),
('eskiz_token_refresh_hours', '23', 'Интервал обновления токена Eskiz в часах'),
('default_sender_nickname', 'CARPET', 'Никнейм отправителя по умолчанию'),
('max_message_length', '1000', 'Максимальная длина SMS сообщения'),
('enable_delivery_tracking', 'true', 'Включить отслеживание доставки'),
('log_retention_days', '30', 'Количество дней хранения логов')
ON CONFLICT (config_key) DO NOTHING;`
