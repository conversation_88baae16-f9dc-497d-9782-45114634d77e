package services

import (
	"fmt"
	"time"

	"carpet-bulk-sms/internal/models"
	"carpet-bulk-sms/pkg/logger"
	"github.com/jmoiron/sqlx"
)

type QueueService struct {
	db           *sqlx.DB
	eskizService *EskizService
	logger       *logger.Logger
}

func NewQueueService(db *sqlx.DB, eskizService *EskizService, log *logger.Logger) *QueueService {
	return &QueueService{
		db:           db,
		eskizService: eskizService,
		logger:       log,
	}
}

// GetPendingItems получает элементы очереди для обработки
func (s *QueueService) GetPendingItems(limit int) ([]models.QueueItem, error) {
	query := `
		SELECT id, campaign_id, client_id, phone_number, client_name, message, 
		       status, eskiz_message_id, eskiz_response, attempts, max_attempts, 
		       last_attempt_at, error_message, sent_at, delivered_at, created_at, updated_at
		FROM sms_bulk_queue 
		WHERE status = $1 
		ORDER BY created_at ASC 
		LIMIT $2`

	items := []models.QueueItem{}
	err := s.db.Select(&items, query, models.QueueStatusPending, limit)
	if err != nil {
		return nil, fmt.Errorf("failed to get pending items: %w", err)
	}

	return items, nil
}

// ProcessItem обрабатывает элемент очереди
func (s *QueueService) ProcessItem(item *models.QueueItem) *models.ProcessingResult {
	startTime := time.Now()
	
	result := &models.ProcessingResult{
		QueueItemID: item.ID,
	}

	// Обновляем статус на "обрабатывается"
	if err := s.updateItemStatus(item.ID, models.QueueStatusProcessing); err != nil {
		result.ErrorMessage = fmt.Sprintf("Failed to update status to processing: %v", err)
		return result
	}

	// Формируем callback URL
	callbackURL := fmt.Sprintf("%s/api/v1/callback/eskiz?queue_id=%d", 
		s.eskizService.cfg.CallbackBaseUrl, item.ID)

	// Отправляем SMS
	eskizResp, err := s.eskizService.SendSMS(item.PhoneNumber, item.Message, callbackURL)
	result.ProcessingTime = time.Since(startTime)

	if err != nil {
		// Ошибка отправки
		result.Success = false
		result.ErrorMessage = err.Error()
		
		// Увеличиваем счетчик попыток
		newAttempts := item.Attempts + 1
		newStatus := models.QueueStatusFailed
		
		// Если еще можно повторить, ставим статус pending
		if newAttempts < item.MaxAttempts {
			newStatus = models.QueueStatusPending
		}

		updateErr := s.updateItemAfterFailure(item.ID, newAttempts, newStatus, err.Error())
		if updateErr != nil {
			s.logger.Error("Failed to update item after failure: %v", updateErr)
		}

		s.logger.SMS("send_failed", item.PhoneNumber, item.CampaignID, false, err.Error())
		return result
	}

	// Успешная отправка
	result.Success = true
	result.EskizMessageID = eskizResp.ID
	result.EskizResponse = *eskizResp

	// Обновляем элемент очереди
	updateErr := s.updateItemAfterSuccess(item.ID, eskizResp)
	if updateErr != nil {
		s.logger.Error("Failed to update item after success: %v", updateErr)
	}

	s.logger.SMS("send_success", item.PhoneNumber, item.CampaignID, true, 
		fmt.Sprintf("Eskiz ID: %s", eskizResp.ID))

	return result
}

// updateItemStatus обновляет статус элемента очереди
func (s *QueueService) updateItemStatus(itemID int64, status int) error {
	query := `
		UPDATE sms_bulk_queue 
		SET status = $1, updated_at = $2 
		WHERE id = $3`

	_, err := s.db.Exec(query, status, time.Now(), itemID)
	return err
}

// updateItemAfterSuccess обновляет элемент после успешной отправки
func (s *QueueService) updateItemAfterSuccess(itemID int64, eskizResp *models.EskizResponse) error {
	query := `
		UPDATE sms_bulk_queue 
		SET status = $1, eskiz_message_id = $2, eskiz_response = $3, 
		    attempts = attempts + 1, last_attempt_at = $4, sent_at = $4, updated_at = $4
		WHERE id = $5`

	_, err := s.db.Exec(query, 
		models.QueueStatusSent, 
		eskizResp.ID, 
		eskizResp, 
		time.Now(), 
		itemID)

	return err
}

// updateItemAfterFailure обновляет элемент после неудачной отправки
func (s *QueueService) updateItemAfterFailure(itemID int64, attempts, status int, errorMsg string) error {
	query := `
		UPDATE sms_bulk_queue 
		SET status = $1, attempts = $2, last_attempt_at = $3, 
		    error_message = $4, updated_at = $3
		WHERE id = $5`

	_, err := s.db.Exec(query, status, attempts, time.Now(), errorMsg, itemID)
	return err
}

// HandleCallback обрабатывает callback от Eskiz
func (s *QueueService) HandleCallback(queueID int64, status string) error {
	// Получаем элемент очереди
	item, err := s.getQueueItem(queueID)
	if err != nil {
		return fmt.Errorf("failed to get queue item: %w", err)
	}

	// Определяем новый статус на основе callback
	var newStatus int
	switch status {
	case "DELIVERED":
		newStatus = models.QueueStatusDelivered
	case "FAILED", "REJECTED":
		newStatus = models.QueueStatusFailed
	default:
		// Неизвестный статус, не обновляем
		s.logger.Warn("Unknown callback status: %s for queue item %d", status, queueID)
		return nil
	}

	// Обновляем статус
	now := time.Now()
	query := `
		UPDATE sms_bulk_queue 
		SET status = $1, updated_at = $2`

	args := []interface{}{newStatus, now}
	argIndex := 3

	if newStatus == models.QueueStatusDelivered {
		query += fmt.Sprintf(", delivered_at = $%d", argIndex)
		args = append(args, now)
		argIndex++
	}

	query += fmt.Sprintf(" WHERE id = $%d", argIndex)
	args = append(args, queueID)

	_, err = s.db.Exec(query, args...)
	if err != nil {
		return fmt.Errorf("failed to update queue item status: %w", err)
	}

	s.logger.SMS("callback_received", item.PhoneNumber, item.CampaignID, 
		newStatus == models.QueueStatusDelivered, 
		fmt.Sprintf("Status: %s", status))

	return nil
}

// getQueueItem получает элемент очереди по ID
func (s *QueueService) getQueueItem(id int64) (*models.QueueItem, error) {
	item := &models.QueueItem{}
	query := `
		SELECT id, campaign_id, client_id, phone_number, client_name, message, 
		       status, eskiz_message_id, eskiz_response, attempts, max_attempts, 
		       last_attempt_at, error_message, sent_at, delivered_at, created_at, updated_at
		FROM sms_bulk_queue 
		WHERE id = $1`

	err := s.db.Get(item, query, id)
	if err != nil {
		return nil, err
	}

	return item, nil
}

// GetQueueStats получает статистику очереди
func (s *QueueService) GetQueueStats() (*models.QueueStatsResponse, error) {
	query := `
		SELECT 
			COUNT(*) as total,
			COUNT(*) FILTER (WHERE status = $1) as pending,
			COUNT(*) FILTER (WHERE status = $2) as processing,
			COUNT(*) FILTER (WHERE status = $3) as sent,
			COUNT(*) FILTER (WHERE status = $4) as delivered,
			COUNT(*) FILTER (WHERE status = $5) as failed,
			COUNT(*) FILTER (WHERE status = $6) as cancelled
		FROM sms_bulk_queue`

	stats := &models.QueueStatsResponse{}
	err := s.db.QueryRow(query,
		models.QueueStatusPending,
		models.QueueStatusProcessing,
		models.QueueStatusSent,
		models.QueueStatusDelivered,
		models.QueueStatusFailed,
		models.QueueStatusCancelled,
	).Scan(
		&stats.Total,
		&stats.Pending,
		&stats.Processing,
		&stats.Sent,
		&stats.Delivered,
		&stats.Failed,
		&stats.Cancelled,
	)

	if err != nil {
		return nil, fmt.Errorf("failed to get queue stats: %w", err)
	}

	return stats, nil
}

// GetCampaignQueueStatus получает статистику очереди для конкретной кампании
func (s *QueueService) GetCampaignQueueStatus(campaignID int64) (*models.QueueStatus, error) {
	query := `
		SELECT 
			COUNT(*) FILTER (WHERE status = $1) as pending,
			COUNT(*) FILTER (WHERE status = $2) as processing,
			COUNT(*) FILTER (WHERE status = $3) as sent,
			COUNT(*) FILTER (WHERE status = $4) as delivered,
			COUNT(*) FILTER (WHERE status = $5) as failed,
			COUNT(*) FILTER (WHERE status = $6) as cancelled
		FROM sms_bulk_queue 
		WHERE campaign_id = $7`

	status := &models.QueueStatus{}
	err := s.db.QueryRow(query,
		models.QueueStatusPending,
		models.QueueStatusProcessing,
		models.QueueStatusSent,
		models.QueueStatusDelivered,
		models.QueueStatusFailed,
		models.QueueStatusCancelled,
		campaignID,
	).Scan(
		&status.Pending,
		&status.Processing,
		&status.Sent,
		&status.Delivered,
		&status.Failed,
		&status.Cancelled,
	)

	if err != nil {
		return nil, fmt.Errorf("failed to get campaign queue status: %w", err)
	}

	return status, nil
}

// RetryFailedItems повторно отправляет неудачные элементы
func (s *QueueService) RetryFailedItems(campaignID *int64, itemIDs []int64) (int, error) {
	query := `
		UPDATE sms_bulk_queue 
		SET status = $1, attempts = 0, error_message = NULL, updated_at = $2 
		WHERE status = $3 AND attempts < max_attempts`

	args := []interface{}{models.QueueStatusPending, time.Now(), models.QueueStatusFailed}
	argIndex := 4

	if campaignID != nil {
		query += fmt.Sprintf(" AND campaign_id = $%d", argIndex)
		args = append(args, *campaignID)
		argIndex++
	}

	if len(itemIDs) > 0 {
		placeholders := ""
		for i, itemID := range itemIDs {
			if i > 0 {
				placeholders += ","
			}
			placeholders += fmt.Sprintf("$%d", argIndex)
			args = append(args, itemID)
			argIndex++
		}
		query += fmt.Sprintf(" AND id IN (%s)", placeholders)
	}

	result, err := s.db.Exec(query, args...)
	if err != nil {
		return 0, fmt.Errorf("failed to retry failed items: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return 0, fmt.Errorf("failed to get rows affected: %w", err)
	}

	return int(rowsAffected), nil
}

// ClearFailedItems удаляет неудачные элементы
func (s *QueueService) ClearFailedItems(campaignID *int64) (int, error) {
	query := `DELETE FROM sms_bulk_queue WHERE status = $1`
	args := []interface{}{models.QueueStatusFailed}

	if campaignID != nil {
		query += " AND campaign_id = $2"
		args = append(args, *campaignID)
	}

	result, err := s.db.Exec(query, args...)
	if err != nil {
		return 0, fmt.Errorf("failed to clear failed items: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return 0, fmt.Errorf("failed to get rows affected: %w", err)
	}

	return int(rowsAffected), nil
}
