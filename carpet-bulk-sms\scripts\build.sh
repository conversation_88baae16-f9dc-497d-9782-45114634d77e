#!/bin/bash

# Скрипт сборки Go приложения для массовых SMS

set -e

echo "🔨 Building Carpet Bulk SMS Service..."

# Переходим в корневую директорию проекта
cd "$(dirname "$0")/.."

# Проверяем наличие Go
if ! command -v go &> /dev/null; then
    echo "❌ Go is not installed"
    exit 1
fi

# Устанавливаем зависимости
echo "📦 Installing dependencies..."
go mod tidy
go mod download

# Создаем директории
echo "📁 Creating directories..."
mkdir -p logs
mkdir -p config

# Сборка приложения
echo "🔧 Building application..."
CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -ldflags="-w -s" -o carpet-bulk-sms cmd/main.go

# Проверяем успешность сборки
if [ -f "carpet-bulk-sms" ]; then
    echo "✅ Build successful!"
    echo "📄 Binary: carpet-bulk-sms"
    echo "📊 Size: $(du -h carpet-bulk-sms | cut -f1)"
else
    echo "❌ Build failed!"
    exit 1
fi

# Делаем исполняемым
chmod +x carpet-bulk-sms

echo "🎉 Build completed successfully!"
echo ""
echo "Next steps:"
echo "1. Configure .env file in config/ directory"
echo "2. Run database migrations"
echo "3. Start the service with ./scripts/start.sh"
