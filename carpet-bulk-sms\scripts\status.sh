#!/bin/bash

# Скрипт проверки статуса Go сервиса массовых SMS

echo "📊 Carpet Bulk SMS Service Status"
echo "================================="

# Переходим в корневую директорию проекта
cd "$(dirname "$0")/.."

PID_FILE="carpet-bulk-sms.pid"

# Проверяем PID файл
if [ -f "$PID_FILE" ]; then
    PID=$(cat "$PID_FILE")
    echo "📄 PID file: $PID_FILE"
    echo "🆔 PID: $PID"
    
    # Проверяем, что процесс запущен
    if ps -p "$PID" > /dev/null 2>&1; then
        echo "✅ Status: RUNNING"
        
        # Показываем информацию о процессе
        echo "⏰ Started: $(ps -o lstart= -p "$PID" 2>/dev/null || echo 'Unknown')"
        echo "💾 Memory: $(ps -o rss= -p "$PID" 2>/dev/null | awk '{print $1/1024 " MB"}' || echo 'Unknown')"
        echo "⚡ CPU: $(ps -o %cpu= -p "$PID" 2>/dev/null || echo 'Unknown')%"
        
        # Проверяем HTTP endpoint
        echo ""
        echo "🌐 HTTP Health Check:"
        if command -v curl &> /dev/null; then
            HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8080/health 2>/dev/null || echo "000")
            if [ "$HTTP_STATUS" = "200" ]; then
                echo "✅ HTTP: OK (200)"
                
                # Получаем детальную информацию
                HEALTH_INFO=$(curl -s http://localhost:8080/health 2>/dev/null || echo "{}")
                echo "📋 Service Info:"
                echo "$HEALTH_INFO" | grep -o '"service":"[^"]*"' | cut -d'"' -f4 | sed 's/^/   Service: /' || true
                echo "$HEALTH_INFO" | grep -o '"status":"[^"]*"' | cut -d'"' -f4 | sed 's/^/   Status: /' || true
                echo "$HEALTH_INFO" | grep -o '"database":"[^"]*"' | cut -d'"' -f4 | sed 's/^/   Database: /' || true
            else
                echo "❌ HTTP: ERROR ($HTTP_STATUS)"
            fi
        else
            echo "⚠️  curl not available, skipping HTTP check"
        fi
        
    else
        echo "❌ Status: NOT RUNNING (stale PID file)"
        echo "🧹 Removing stale PID file..."
        rm -f "$PID_FILE"
    fi
else
    echo "📄 PID file: Not found"
    
    # Ищем процессы по имени
    PIDS=$(pgrep -f "carpet-bulk-sms" 2>/dev/null || true)
    if [ -n "$PIDS" ]; then
        echo "⚠️  Status: RUNNING (no PID file)"
        echo "🔍 Found processes: $PIDS"
        echo "💡 Consider using ./scripts/stop.sh and ./scripts/start.sh to manage properly"
    else
        echo "❌ Status: NOT RUNNING"
    fi
fi

echo ""
echo "📁 Log Files:"
if [ -f "logs/service.log" ]; then
    LOG_SIZE=$(du -h logs/service.log | cut -f1)
    LOG_LINES=$(wc -l < logs/service.log)
    echo "   📄 logs/service.log ($LOG_SIZE, $LOG_LINES lines)"
    echo "   📖 Last 3 lines:"
    tail -n 3 logs/service.log | sed 's/^/      /'
else
    echo "   📄 No log files found"
fi

echo ""
echo "🔧 Available Commands:"
echo "   ./scripts/start.sh   - Start the service"
echo "   ./scripts/stop.sh    - Stop the service"
echo "   ./scripts/restart.sh - Restart the service"
echo "   ./scripts/build.sh   - Build the service"
