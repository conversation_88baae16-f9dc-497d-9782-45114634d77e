package workers

import (
	"context"
	"sync"
	"time"

	"carpet-bulk-sms/config"
	"carpet-bulk-sms/internal/models"
	"carpet-bulk-sms/internal/services"
	"carpet-bulk-sms/pkg/logger"
)

type QueueProcessor struct {
	queueService *services.QueueService
	cfg          *config.Config
	logger       *logger.Logger
	ctx          context.Context
	cancel       context.CancelFunc
	wg           sync.WaitGroup
	running      bool
	mu           sync.RWMutex
}

func NewQueueProcessor(queueService *services.QueueService, cfg *config.Config, log *logger.Logger) *QueueProcessor {
	ctx, cancel := context.WithCancel(context.Background())
	
	return &QueueProcessor{
		queueService: queueService,
		cfg:          cfg,
		logger:       log,
		ctx:          ctx,
		cancel:       cancel,
	}
}

// Start запускает обработчик очереди
func (p *QueueProcessor) Start() {
	p.mu.Lock()
	if p.running {
		p.mu.Unlock()
		return
	}
	p.running = true
	p.mu.Unlock()

	p.logger.Info("Queue processor started")

	ticker := time.NewTicker(p.cfg.QueueProcessInterval)
	defer ticker.Stop()

	for {
		select {
		case <-p.ctx.Done():
			p.logger.Info("Queue processor stopped")
			return
		case <-ticker.C:
			p.processQueue()
		}
	}
}

// Stop останавливает обработчик очереди
func (p *QueueProcessor) Stop() {
	p.mu.Lock()
	if !p.running {
		p.mu.Unlock()
		return
	}
	p.running = false
	p.mu.Unlock()

	p.logger.Info("Stopping queue processor...")
	p.cancel()
	p.wg.Wait()
	p.logger.Info("Queue processor stopped")
}

// IsRunning проверяет, запущен ли обработчик
func (p *QueueProcessor) IsRunning() bool {
	p.mu.RLock()
	defer p.mu.RUnlock()
	return p.running
}

// processQueue обрабатывает очередь SMS
func (p *QueueProcessor) processQueue() {
	// Получаем элементы для обработки
	items, err := p.queueService.GetPendingItems(p.cfg.QueueBatchSize)
	if err != nil {
		p.logger.Error("Failed to get pending items: %v", err)
		return
	}

	if len(items) == 0 {
		return // Нет элементов для обработки
	}

	p.logger.Queue("processing_batch", len(items), 0, 0)

	// Создаем семафор для ограничения количества одновременных отправок
	semaphore := make(chan struct{}, p.cfg.MaxConcurrentSends)
	
	var processed, failed int
	var mu sync.Mutex

	// Обрабатываем элементы параллельно
	for _, item := range items {
		p.wg.Add(1)
		
		go func(item models.QueueItem) {
			defer p.wg.Done()
			
			// Захватываем семафор
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			// Проверяем, не остановлен ли процессор
			select {
			case <-p.ctx.Done():
				return
			default:
			}

			// Обрабатываем элемент
			result := p.queueService.ProcessItem(&item)
			
			mu.Lock()
			processed++
			if !result.Success {
				failed++
			}
			mu.Unlock()

			// Логируем результат
			if result.Success {
				p.logger.Performance("sms_send", result.ProcessingTime, map[string]interface{}{
					"queue_item_id":    result.QueueItemID,
					"eskiz_message_id": result.EskizMessageID,
					"phone":            item.PhoneNumber,
				})
			} else {
				p.logger.Error("Failed to process queue item %d: %s", result.QueueItemID, result.ErrorMessage)
			}
		}(item)
	}

	// Ждем завершения всех горутин
	p.wg.Wait()

	p.logger.Queue("batch_completed", len(items), processed, failed)

	// Обновляем статистику кампаний
	p.updateCampaignStats(items)
}

// updateCampaignStats обновляет статистику кампаний
func (p *QueueProcessor) updateCampaignStats(items []models.QueueItem) {
	// Собираем уникальные ID кампаний
	campaignIDs := make(map[int64]bool)
	for _, item := range items {
		campaignIDs[item.CampaignID] = true
	}

	// Обновляем статистику для каждой кампании
	for campaignID := range campaignIDs {
		// Здесь должен быть вызов сервиса кампаний для обновления статистики
		// Но поскольку у нас нет прямого доступа к CampaignService,
		// мы можем сделать это через базу данных или добавить метод в QueueService
		p.logger.Debug("Should update stats for campaign %d", campaignID)
	}
}

// GetStats возвращает статистику обработчика очереди
func (p *QueueProcessor) GetStats() map[string]interface{} {
	stats := map[string]interface{}{
		"running":              p.IsRunning(),
		"queue_batch_size":     p.cfg.QueueBatchSize,
		"max_concurrent_sends": p.cfg.MaxConcurrentSends,
		"process_interval":     p.cfg.QueueProcessInterval.String(),
	}

	// Добавляем статистику очереди
	queueStats, err := p.queueService.GetQueueStats()
	if err != nil {
		p.logger.Error("Failed to get queue stats: %v", err)
	} else {
		stats["queue_total"] = queueStats.Total
		stats["queue_pending"] = queueStats.Pending
		stats["queue_processing"] = queueStats.Processing
		stats["queue_sent"] = queueStats.Sent
		stats["queue_delivered"] = queueStats.Delivered
		stats["queue_failed"] = queueStats.Failed
		stats["queue_cancelled"] = queueStats.Cancelled
	}

	return stats
}
