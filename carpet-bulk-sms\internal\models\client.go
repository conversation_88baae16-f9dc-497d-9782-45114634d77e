package models

import (
	"database/sql"
	"time"
)

// Client модель клиента (только для чтения из существующей таблицы)
type Client struct {
	ID           int64          `db:"id" json:"id"`
	FullName     sql.NullString `db:"full_name" json:"full_name"`
	PhoneNumber  sql.NullString `db:"phone_number" json:"phone_number"`
	PhoneNumber2 sql.NullString `db:"phone_number_2" json:"phone_number_2"`
	Address      sql.NullString `db:"address" json:"address"`
	Status       int            `db:"status" json:"status"`
	RegionID     sql.NullInt64  `db:"region_id" json:"region_id"`
	CreatedAt    time.Time      `db:"created_at" json:"created_at"`
	DeletedAt    sql.NullTime   `db:"deleted_at" json:"deleted_at"`
}

// ClientFilter фильтры для поиска клиентов
type ClientFilter struct {
	RegionIDs     []int64 `json:"region_ids,omitempty"`
	Status        *int    `json:"status,omitempty"`
	CreatedAfter  *string `json:"created_after,omitempty"`
	CreatedBefore *string `json:"created_before,omitempty"`
	HasPhone      *bool   `json:"has_phone,omitempty"`
	Limit         int     `json:"limit,omitempty"`
	Offset        int     `json:"offset,omitempty"`
}

// IsActive проверяет, активен ли клиент
func (c *Client) IsActive() bool {
	return c.Status == 1 && !c.DeletedAt.Valid
}

// GetPhoneNumber возвращает основной номер телефона
func (c *Client) GetPhoneNumber() string {
	if c.PhoneNumber.Valid {
		return c.PhoneNumber.String
	}
	return ""
}

// GetName возвращает имя клиента
func (c *Client) GetName() string {
	if c.FullName.Valid {
		return c.FullName.String
	}
	return ""
}

// HasValidPhone проверяет, есть ли у клиента валидный номер телефона
func (c *Client) HasValidPhone() bool {
	return c.PhoneNumber.Valid && c.PhoneNumber.String != ""
}

// ClientStats статистика по клиентам
type ClientStats struct {
	Total       int64 `json:"total"`
	Active      int64 `json:"active"`
	WithPhone   int64 `json:"with_phone"`
	ByRegion    map[int64]int64 `json:"by_region"`
}

// Region модель региона (только для чтения)
type Region struct {
	ID        int64          `db:"id" json:"id"`
	Name      string         `db:"name" json:"name"`
	CreatedAt time.Time      `db:"created_at" json:"created_at"`
	DeletedAt sql.NullTime   `db:"deleted_at" json:"deleted_at"`
}
