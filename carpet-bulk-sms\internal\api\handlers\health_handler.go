package handlers

import (
	"net/http"
	"time"

	"carpet-bulk-sms/config"
	"carpet-bulk-sms/internal/database"
	"carpet-bulk-sms/pkg/logger"
	"github.com/gin-gonic/gin"
)

type HealthHandler struct {
	cfg    *config.Config
	logger *logger.Logger
}

func NewHealthHandler(cfg *config.Config, log *logger.Logger) *HealthHandler {
	return &HealthHandler{
		cfg:    cfg,
		logger: log,
	}
}

// Health проверяет здоровье сервиса
func (h *HealthHandler) Health(c *gin.Context) {
	status := gin.H{
		"service": h.cfg.ServiceName,
		"status":  "healthy",
		"time":    time.Now().Format(time.RFC3339),
		"version": "1.0.0",
	}

	// Проверяем подключение к базе данных
	if database.DB != nil {
		if err := database.DB.Ping(); err != nil {
			status["status"] = "unhealthy"
			status["database"] = "disconnected"
			status["error"] = err.Error()
			c.<PERSON>(http.StatusServiceUnavailable, status)
			return
		}
		status["database"] = "connected"
	} else {
		status["status"] = "unhealthy"
		status["database"] = "not_initialized"
		c.JSON(http.StatusServiceUnavailable, status)
		return
	}

	c.JSON(http.StatusOK, status)
}

// Ready проверяет готовность сервиса
func (h *HealthHandler) Ready(c *gin.Context) {
	ready := gin.H{
		"service": h.cfg.ServiceName,
		"ready":   true,
		"time":    time.Now().Format(time.RFC3339),
	}

	// Проверяем все критичные компоненты
	checks := make(map[string]interface{})

	// Проверка базы данных
	if database.DB != nil {
		if err := database.DB.Ping(); err != nil {
			checks["database"] = gin.H{
				"status": "fail",
				"error":  err.Error(),
			}
			ready["ready"] = false
		} else {
			checks["database"] = gin.H{
				"status": "pass",
			}
		}
	} else {
		checks["database"] = gin.H{
			"status": "fail",
			"error":  "database not initialized",
		}
		ready["ready"] = false
	}

	// Проверка конфигурации
	if h.cfg.EskizLogin == "" || h.cfg.EskizPassword == "" {
		checks["eskiz_config"] = gin.H{
			"status": "fail",
			"error":  "eskiz credentials not configured",
		}
		ready["ready"] = false
	} else {
		checks["eskiz_config"] = gin.H{
			"status": "pass",
		}
	}

	ready["checks"] = checks

	if ready["ready"].(bool) {
		c.JSON(http.StatusOK, ready)
	} else {
		c.JSON(http.StatusServiceUnavailable, ready)
	}
}
