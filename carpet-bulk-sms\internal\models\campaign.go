package models

import (
	"database/sql"
	"time"
)

// Campaign представляет кампанию массовых SMS
type Campaign struct {
	ID               int64     `db:"id" json:"id"`
	Name             string    `db:"name" json:"name"`
	Message          string    `db:"message" json:"message"`
	SenderNickname   string    `db:"sender_nickname" json:"sender_nickname"`
	TotalRecipients  int       `db:"total_recipients" json:"total_recipients"`
	SentCount        int       `db:"sent_count" json:"sent_count"`
	DeliveredCount   int       `db:"delivered_count" json:"delivered_count"`
	FailedCount      int       `db:"failed_count" json:"failed_count"`
	Status           int       `db:"status" json:"status"`
	CreatedByUserID  sql.NullInt64 `db:"created_by_user_id" json:"created_by_user_id"`
	CreatedAt        time.Time `db:"created_at" json:"created_at"`
	StartedAt        sql.NullTime `db:"started_at" json:"started_at"`
	CompletedAt      sql.NullTime `db:"completed_at" json:"completed_at"`
	Priority         int       `db:"priority" json:"priority"`
	RetryFailed      bool      `db:"retry_failed" json:"retry_failed"`
	MaxRetries       int       `db:"max_retries" json:"max_retries"`
}

// CampaignStatus константы статусов кампании
const (
	CampaignStatusCreated    = 0
	CampaignStatusProcessing = 1
	CampaignStatusCompleted  = 2
	CampaignStatusCancelled  = 3
	CampaignStatusFailed     = 4
)

// GetStatusText возвращает текстовое описание статуса
func (c *Campaign) GetStatusText() string {
	switch c.Status {
	case CampaignStatusCreated:
		return "Создана"
	case CampaignStatusProcessing:
		return "Обрабатывается"
	case CampaignStatusCompleted:
		return "Завершена"
	case CampaignStatusCancelled:
		return "Отменена"
	case CampaignStatusFailed:
		return "Ошибка"
	default:
		return "Неизвестно"
	}
}

// IsActive проверяет, активна ли кампания
func (c *Campaign) IsActive() bool {
	return c.Status == CampaignStatusCreated || c.Status == CampaignStatusProcessing
}

// CanBeStarted проверяет, можно ли запустить кампанию
func (c *Campaign) CanBeStarted() bool {
	return c.Status == CampaignStatusCreated
}

// CanBeStopped проверяет, можно ли остановить кампанию
func (c *Campaign) CanBeStopped() bool {
	return c.Status == CampaignStatusProcessing
}

// GetProgressPercent возвращает процент выполнения
func (c *Campaign) GetProgressPercent() float64 {
	if c.TotalRecipients == 0 {
		return 0
	}
	processed := c.SentCount + c.FailedCount
	return float64(processed) / float64(c.TotalRecipients) * 100
}

// GetSuccessRate возвращает процент успешных отправок
func (c *Campaign) GetSuccessRate() float64 {
	if c.SentCount == 0 {
		return 0
	}
	return float64(c.DeliveredCount) / float64(c.SentCount) * 100
}

// CreateCampaignRequest запрос на создание кампании
type CreateCampaignRequest struct {
	Name           string      `json:"name" binding:"required,min=1,max=255"`
	Message        string      `json:"message" binding:"required,min=1,max=1000"`
	SenderNickname string      `json:"sender_nickname,omitempty"`
	Priority       int         `json:"priority,omitempty"`
	Recipients     []Recipient `json:"recipients" binding:"required,min=1"`
}

// CreateCampaignByFilterRequest запрос на создание кампании по фильтрам
type CreateCampaignByFilterRequest struct {
	Name           string                 `json:"name" binding:"required,min=1,max=255"`
	Message        string                 `json:"message" binding:"required,min=1,max=1000"`
	SenderNickname string                 `json:"sender_nickname,omitempty"`
	Priority       int                    `json:"priority,omitempty"`
	Filters        map[string]interface{} `json:"filters" binding:"required"`
}

// Recipient получатель SMS
type Recipient struct {
	ClientID int64  `json:"client_id,omitempty"`
	Phone    string `json:"phone" binding:"required"`
	Name     string `json:"name,omitempty"`
}

// CampaignListResponse ответ со списком кампаний
type CampaignListResponse struct {
	Campaigns  []Campaign `json:"campaigns"`
	Total      int64      `json:"total"`
	Page       int        `json:"page"`
	Limit      int        `json:"limit"`
	TotalPages int        `json:"total_pages"`
}

// CampaignStatusResponse ответ со статусом кампании
type CampaignStatusResponse struct {
	Campaign        Campaign `json:"campaign"`
	QueueStatus     QueueStatus `json:"queue_status"`
	RecentActivity  []QueueItem `json:"recent_activity,omitempty"`
}

// QueueStatus статус очереди для кампании
type QueueStatus struct {
	Pending    int `json:"pending"`
	Processing int `json:"processing"`
	Sent       int `json:"sent"`
	Delivered  int `json:"delivered"`
	Failed     int `json:"failed"`
	Cancelled  int `json:"cancelled"`
}
