package models

import (
	"database/sql"
	"database/sql/driver"
	"encoding/json"
	"time"
)

// QueueItem элемент очереди SMS
type QueueItem struct {
	ID              int64           `db:"id" json:"id"`
	CampaignID      int64           `db:"campaign_id" json:"campaign_id"`
	ClientID        sql.NullInt64   `db:"client_id" json:"client_id"`
	PhoneNumber     string          `db:"phone_number" json:"phone_number"`
	ClientName      sql.NullString  `db:"client_name" json:"client_name"`
	Message         string          `db:"message" json:"message"`
	Status          int             `db:"status" json:"status"`
	EskizMessageID  sql.NullString  `db:"eskiz_message_id" json:"eskiz_message_id"`
	EskizResponse   EskizResponse   `db:"eskiz_response" json:"eskiz_response"`
	Attempts        int             `db:"attempts" json:"attempts"`
	MaxAttempts     int             `db:"max_attempts" json:"max_attempts"`
	LastAttemptAt   sql.NullTime    `db:"last_attempt_at" json:"last_attempt_at"`
	ErrorMessage    sql.NullString  `db:"error_message" json:"error_message"`
	SentAt          sql.NullTime    `db:"sent_at" json:"sent_at"`
	DeliveredAt     sql.NullTime    `db:"delivered_at" json:"delivered_at"`
	CreatedAt       time.Time       `db:"created_at" json:"created_at"`
	UpdatedAt       time.Time       `db:"updated_at" json:"updated_at"`
}

// QueueItemStatus константы статусов элементов очереди
const (
	QueueStatusPending    = 0
	QueueStatusProcessing = 1
	QueueStatusSent       = 2
	QueueStatusDelivered  = 3
	QueueStatusFailed     = 4
	QueueStatusCancelled  = 5
)

// GetStatusText возвращает текстовое описание статуса
func (q *QueueItem) GetStatusText() string {
	switch q.Status {
	case QueueStatusPending:
		return "В ожидании"
	case QueueStatusProcessing:
		return "Обрабатывается"
	case QueueStatusSent:
		return "Отправлено"
	case QueueStatusDelivered:
		return "Доставлено"
	case QueueStatusFailed:
		return "Ошибка"
	case QueueStatusCancelled:
		return "Отменено"
	default:
		return "Неизвестно"
	}
}

// CanRetry проверяет, можно ли повторить отправку
func (q *QueueItem) CanRetry() bool {
	return q.Status == QueueStatusFailed && q.Attempts < q.MaxAttempts
}

// IsCompleted проверяет, завершена ли обработка элемента
func (q *QueueItem) IsCompleted() bool {
	return q.Status == QueueStatusDelivered || q.Status == QueueStatusFailed || q.Status == QueueStatusCancelled
}

// EskizResponse структура для хранения ответа от Eskiz API
type EskizResponse struct {
	Status    string      `json:"status,omitempty"`
	Message   string      `json:"message,omitempty"`
	ID        string      `json:"id,omitempty"`
	Data      interface{} `json:"data,omitempty"`
	Timestamp time.Time   `json:"timestamp,omitempty"`
}

// Scan реализует интерфейс sql.Scanner для EskizResponse
func (er *EskizResponse) Scan(value interface{}) error {
	if value == nil {
		*er = EskizResponse{}
		return nil
	}

	switch v := value.(type) {
	case []byte:
		return json.Unmarshal(v, er)
	case string:
		return json.Unmarshal([]byte(v), er)
	default:
		return nil
	}
}

// Value реализует интерфейс driver.Valuer для EskizResponse
func (er EskizResponse) Value() (driver.Value, error) {
	if er.Status == "" && er.Message == "" && er.ID == "" {
		return nil, nil
	}
	return json.Marshal(er)
}

// BulkQueueRequest запрос на добавление элементов в очередь
type BulkQueueRequest struct {
	CampaignID int64       `json:"campaign_id" binding:"required"`
	Items      []QueueItem `json:"items" binding:"required,min=1"`
}

// QueueStatsResponse статистика очереди
type QueueStatsResponse struct {
	Total      int64 `json:"total"`
	Pending    int64 `json:"pending"`
	Processing int64 `json:"processing"`
	Sent       int64 `json:"sent"`
	Delivered  int64 `json:"delivered"`
	Failed     int64 `json:"failed"`
	Cancelled  int64 `json:"cancelled"`
}

// QueueListResponse ответ со списком элементов очереди
type QueueListResponse struct {
	Items      []QueueItem `json:"items"`
	Total      int64       `json:"total"`
	Page       int         `json:"page"`
	Limit      int         `json:"limit"`
	TotalPages int         `json:"total_pages"`
}

// RetryRequest запрос на повторную отправку
type RetryRequest struct {
	CampaignID int64   `json:"campaign_id,omitempty"`
	ItemIDs    []int64 `json:"item_ids,omitempty"`
	OnlyFailed bool    `json:"only_failed"`
}

// CallbackRequest запрос callback от Eskiz
type CallbackRequest struct {
	MessageID string `json:"message_id" form:"message_id"`
	Status    string `json:"status" form:"status"`
	Phone     string `json:"phone" form:"phone"`
	Timestamp string `json:"timestamp" form:"timestamp"`
}

// ProcessingResult результат обработки элемента очереди
type ProcessingResult struct {
	QueueItemID    int64         `json:"queue_item_id"`
	Success        bool          `json:"success"`
	EskizMessageID string        `json:"eskiz_message_id,omitempty"`
	ErrorMessage   string        `json:"error_message,omitempty"`
	EskizResponse  EskizResponse `json:"eskiz_response,omitempty"`
	ProcessingTime time.Duration `json:"processing_time"`
}
